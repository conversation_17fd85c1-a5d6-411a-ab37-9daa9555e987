<!-- Template cho DataTable view với order configuration và advanced filtering -->
<!-- Thay thế các placeholder bằng gi<PERSON> trị thực tế -->



<script>
$(document).ready(function () {
    dataTable = $('#dataTable').DataTable({
        dom: '<"top d-flex flex-wrap gap-2 justify-content-between align-items-center mb-2"pf>rt<"bottom d-flex flex-wrap gap-2 justify-content-between align-items-center mt-2"il><"clear">',
        serverSide: true,
        processing: true,
        responsive: true,
        pageLength: 25,
        lengthMenu: [25, 50, 75, 100],
        paging: true,
        scrollX: true,
        ajax: {
            url: '/CONTROLLER_PATH/list/', // Thay bằng đường dẫn controller thực tế
            method: 'POST',
            dataType: "json",
            beforeSend: function() {
                loading.show();
            },
            complete: function() {
                loading.hide();
            },
            dataSrc: function(response){
                if (response.data) {
                    return response.data;
                } else {
                    return [];
                }
            },
        },
        rowId: function(row) {
            return 'ENTITY_NAME-' + row.id; // Thay ENTITY_NAME bằng tên entity
        },
        columns: [
            {
                data: null,
                orderable: false, // Không cho phép sort cột actions
                searchable: false, // Không search trong cột actions
                render: function (data, type, row) {
                    return `
                        <div class="d-flex gap-2">
                            <a class="btn btn-info btn-sm btn-circle" data-id="${row.id}" href="/CONTROLLER_PATH/edit/${row.id}" title="Sửa">
                                <i class="fas fa-pen-square"></i>
                            </a>
                            <button class="btn btn-danger btn-sm btn-circle" data-id="${row.id}" onclick="deleteEntity(${row.id}, '${row.DISPLAY_FIELD}')" title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            },
            { 
                data: 'COLUMN_1', // Thay bằng tên column thực tế
                orderable: true, // Cho phép sort
                searchable: true, // Cho phép search
                render: function(data, type, row) {
                    // Tùy chỉnh cách hiển thị nếu cần
                    return data;
                },
                className: 'min-width-110' // Tùy chỉnh CSS class
            },
            { 
                data: 'COLUMN_2', // Thay bằng tên column thực tế
                orderable: true, // Cho phép sort
                searchable: true, // Cho phép search
                render: function(data, type, row) {
                    // Ví dụ: format số điện thoại
                    return `<a href="tel:${data}">${data}</a>`;
                }
            },
            { 
                data: 'COLUMN_3', // Thay bằng tên column thực tế
                orderable: true, // Cho phép sort
                searchable: false, // Không search cho column này
                render: function(data, type, row) {
                    // Ví dụ: format ngày tháng
                    return data ? moment(data).format('D/M/YYYY') : '';
                }
            },
            // Thêm các column khác...
        ],
        // Cấu hình order mặc định DESC
        order: [[1, 'desc']], // Sort column đầu tiên (không phải actions) theo DESC
        // Cấu hình searching
        searching: true, // Bật tính năng search
        // Cấu hình ordering
        ordering: true, // Bật tính năng sort

        rowCallback: function(row, data) {
            // Thêm logic xử lý row nếu cần
        }
    });
});

function deleteEntity(id, name) {
    confirmDialog('Xác nhận', 'Bạn có muốn xóa ' + name).then(responseData => {
        if(responseData.isConfirmed && id){
            $.ajax({
                type: 'POST',
                url: '/CONTROLLER_PATH/delete', // Thay bằng đường dẫn delete thực tế
                data: {id: id, active: 0},
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success) {
                        toarstMessage('Xóa thành công');
                        document.getElementById('ENTITY_NAME-' + id).remove();
                    } else {
                        toarstError(result.message);
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        }
    });
}
</script>

<!--
HƯỚNG DẪN SỬ DỤNG TEMPLATE:

1. Thay thế các placeholder:
   - CONTROLLER_PATH: Đường dẫn controller (ví dụ: 'patient', 'research')
   - ENTITY_NAME: Tên entity (ví dụ: 'patient', 'research')
   - DISPLAY_FIELD: Field hiển thị trong confirm dialog (ví dụ: 'fullname', 'name')
   - COLUMN_1, COLUMN_2, COLUMN_3: Tên các column thực tế

2. Tùy chỉnh render function cho từng column:
   - Thêm format cho ngày tháng, số điện thoại, link, etc.
   - Thêm CSS class cho styling
   - Thêm logic hiển thị đặc biệt

3. Cấu hình orderable và searchable:
   - orderable: true/false - cho phép sort column
   - searchable: true/false - cho phép search trong column

4. Tùy chỉnh actions column:
   - Thêm/bớt các button action
   - Thay đổi icon và style
   - Thêm logic permission check

VÍ DỤ CỤ THỂ:
- CONTROLLER_PATH: 'patient'
- ENTITY_NAME: 'patient'
- DISPLAY_FIELD: 'fullname'
- COLUMN_1: 'fullname'
- COLUMN_2: 'phone'
- COLUMN_3: 'ngay_hoi_chan'

CÁCH SỬ DỤNG TRONG EJS:
1. Copy phần script vào file .ejs
2. Thay thế các placeholder
3. Tùy chỉnh theo yêu cầu cụ thể
4. Test functionality
-->
