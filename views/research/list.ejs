<!DOCTYPE html>
<html lang="en">
<head>
    <%- include('../layout/head') %>
    <title><PERSON>h sách nghiên cứu - Patients</title>
</head>
<body>

     <!-- Page Wrapper -->
    <div id="wrapper">
        <%- include('../layout/sidebar') %>

         <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <%- include('../layout/header') %>
            <!-- Begin Page Content -->
                <% if(errors.length > 0){%>
                    <div class="container">
                        <div class="box mt-3">
                            <%for(let item of errors){%>
                                <div><%-JSON.stringify(item)%></div>
                            <%}%>
                        </div>
                    </div>
                <% }else{ %>
                    <div class="container-fluid">
                        <!-- DataTales Example -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary"><PERSON>h sách nghiên cứu</h6>
                                <a title="Thêm mới nghiên cứu" class="btn btn-success btn-circle btn-add-patient" data-bs-toggle="modal" href="#modal-add-research">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>Tên</th>
                                                <th>Ghi chú</th>
                                                <th>Thao tác</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>
            <%- include('../layout/footer') %>
        </div>
        
    </div>
    <div class="modal fade" id="modal-add-research" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
          <div class="modal-content">
            <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            <h3 class="modal-title fs-6 text-uppercase text-center mb-3">Thêm nghiên cứu</h3>
            <div class="row flex-wrap g-3">
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Tên nghiên cứu</h6>
                        </div>
                        <div class="card-body">
                            <input type="text" class="form-control" id="rs_name" placeholder="Tên nghiên cứu">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Ghi chú</h6>
                        </div>
                        <div class="card-body">
                            <input type="text" class="form-control" id="note" placeholder="Ghi chú">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row g-2 justify-content-center mt-2">
              <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button"  data-bs-dismiss="modal">Huỷ</button>
              </div>
              <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="addResearch()">Lưu</button>
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="modal fade" id="modal-export-config" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
          <div class="modal-content">
            <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            <h3 class="modal-title fs-6 text-uppercase text-center mb-3">Chọn thông số xuất Excel</h3>
            <div class="row">
                <div class="col-12">
                    <div class="mb-3 d-flex gap-2">
                        <button type="button" class="btn btn-sm btn-success" onclick="selectAllFields()">Chọn tất cả</button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="unselectAllFields()">Bỏ chọn tất cả</button>
                        <button type="button" class="btn btn-sm btn-info" onclick="selectBasicFields()">Chọn cơ bản</button>
                    </div>
                </div>
            </div>
            <div class="row">
                <!-- Thông tin cơ bản -->
                <div class="col-md-3">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Thông tin cơ bản</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="fullname" id="field_fullname" checked>
                                <label class="form-check-label" for="field_fullname">Tên bệnh nhân</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="menu_name" id="field_menu_name" checked>
                                <label class="form-check-label" for="field_menu_name">Tên thực đơn</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Năng lượng và chất dinh dưỡng chính -->
                <div class="col-md-3">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Năng lượng & Chất chính</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="energy" id="field_energy" checked>
                                <label class="form-check-label" for="field_energy">Năng lượng (kcal)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="protein" id="field_protein" checked>
                                <label class="form-check-label" for="field_protein">Protein (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="animal_protein" id="field_animal_protein">
                                <label class="form-check-label" for="field_animal_protein">Protein động vật (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="lipid" id="field_lipid" checked>
                                <label class="form-check-label" for="field_lipid">Lipid (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="unanimal_lipid" id="field_unanimal_lipid">
                                <label class="form-check-label" for="field_unanimal_lipid">Lipid không động vật (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="carbohydrate" id="field_carbohydrate" checked>
                                <label class="form-check-label" for="field_carbohydrate">Carbohydrate (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="fiber" id="field_fiber" checked>
                                <label class="form-check-label" for="field_fiber">Chất xơ (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="water" id="field_water">
                                <label class="form-check-label" for="field_water">Nước (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="ash" id="field_ash">
                                <label class="form-check-label" for="field_ash">Tro (g)</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Chất béo chi tiết -->
                <div class="col-md-3">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Chất béo chi tiết</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="total_fat" id="field_total_fat">
                                <label class="form-check-label" for="field_total_fat">Tổng chất béo (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="total_saturated_fat" id="field_total_saturated_fat">
                                <label class="form-check-label" for="field_total_saturated_fat">Chất béo bão hòa (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="mufa" id="field_mufa">
                                <label class="form-check-label" for="field_mufa">MUFA (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="linoleic" id="field_linoleic">
                                <label class="form-check-label" for="field_linoleic">Linoleic (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="linolenic" id="field_linolenic">
                                <label class="form-check-label" for="field_linolenic">Linolenic (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="arachidonic" id="field_arachidonic">
                                <label class="form-check-label" for="field_arachidonic">Arachidonic (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="trans_fatty_acids" id="field_trans_fatty_acids">
                                <label class="form-check-label" for="field_trans_fatty_acids">Trans fatty acids (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="cholesterol" id="field_cholesterol">
                                <label class="form-check-label" for="field_cholesterol">Cholesterol (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="oleic" id="field_oleic">
                                <label class="form-check-label" for="field_oleic">Oleic (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="palmitic" id="field_palmitic">
                                <label class="form-check-label" for="field_palmitic">Palmitic (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="stearic" id="field_stearic">
                                <label class="form-check-label" for="field_stearic">Stearic (g)</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Vitamin -->
                <div class="col-md-3">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Vitamin</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_a_rae" id="field_vitamin_a_rae">
                                <label class="form-check-label" for="field_vitamin_a_rae">Vitamin A (µg RAE)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="retinol" id="field_retinol">
                                <label class="form-check-label" for="field_retinol">Retinol (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_b1" id="field_vitamin_b1">
                                <label class="form-check-label" for="field_vitamin_b1">Vitamin B1 (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_b2" id="field_vitamin_b2">
                                <label class="form-check-label" for="field_vitamin_b2">Vitamin B2 (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_b6" id="field_vitamin_b6">
                                <label class="form-check-label" for="field_vitamin_b6">Vitamin B6 (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_b12" id="field_vitamin_b12">
                                <label class="form-check-label" for="field_vitamin_b12">Vitamin B12 (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_c" id="field_vitamin_c" checked>
                                <label class="form-check-label" for="field_vitamin_c">Vitamin C (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_e" id="field_vitamin_e">
                                <label class="form-check-label" for="field_vitamin_e">Vitamin E (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_k" id="field_vitamin_k">
                                <label class="form-check-label" for="field_vitamin_k">Vitamin K (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="niacin" id="field_niacin">
                                <label class="form-check-label" for="field_niacin">Niacin (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="folate" id="field_folate">
                                <label class="form-check-label" for="field_folate">Folate (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="thiamine" id="field_thiamine">
                                <label class="form-check-label" for="field_thiamine">Thiamine (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="riboflavin" id="field_riboflavin">
                                <label class="form-check-label" for="field_riboflavin">Riboflavin (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="pantothenic_acid" id="field_pantothenic_acid">
                                <label class="form-check-label" for="field_pantothenic_acid">Pantothenic acid (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="biotin" id="field_biotin">
                                <label class="form-check-label" for="field_biotin">Biotin (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="vitamin_d" id="field_vitamin_d">
                                <label class="form-check-label" for="field_vitamin_d">Vitamin D (µg)</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Khoáng chất -->
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Khoáng chất</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="calci" id="field_calci" checked>
                                <label class="form-check-label" for="field_calci">Canxi (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="phosphorous" id="field_phosphorous">
                                <label class="form-check-label" for="field_phosphorous">Phosphor (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="fe" id="field_fe" checked>
                                <label class="form-check-label" for="field_fe">Sắt (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="zinc" id="field_zinc" checked>
                                <label class="form-check-label" for="field_zinc">Kẽm (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="sodium" id="field_sodium">
                                <label class="form-check-label" for="field_sodium">Natri (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="potassium" id="field_potassium">
                                <label class="form-check-label" for="field_potassium">Kali (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="magnesium" id="field_magnesium">
                                <label class="form-check-label" for="field_magnesium">Magie (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="manganese" id="field_manganese">
                                <label class="form-check-label" for="field_manganese">Mangan (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="copper" id="field_copper">
                                <label class="form-check-label" for="field_copper">Đồng (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="selenium" id="field_selenium">
                                <label class="form-check-label" for="field_selenium">Selenium (µg)</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Amino acid -->
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Amino acid</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="lysin" id="field_lysin">
                                <label class="form-check-label" for="field_lysin">Lysin (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="methionin" id="field_methionin">
                                <label class="form-check-label" for="field_methionin">Methionin (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="tryptophan" id="field_tryptophan">
                                <label class="form-check-label" for="field_tryptophan">Tryptophan (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="phenylalanin" id="field_phenylalanin">
                                <label class="form-check-label" for="field_phenylalanin">Phenylalanin (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="threonin" id="field_threonin">
                                <label class="form-check-label" for="field_threonin">Threonin (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="isoleucine" id="field_isoleucine">
                                <label class="form-check-label" for="field_isoleucine">Isoleucine (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="leucine" id="field_leucine">
                                <label class="form-check-label" for="field_leucine">Leucine (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="valine" id="field_valine">
                                <label class="form-check-label" for="field_valine">Valine (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="arginine" id="field_arginine">
                                <label class="form-check-label" for="field_arginine">Arginine (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="histidine" id="field_histidine">
                                <label class="form-check-label" for="field_histidine">Histidine (mg)</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Khác -->
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header py-2">
                            <h6 class="m-0 font-weight-bold text-primary">Khác</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="total_sugar" id="field_total_sugar">
                                <label class="form-check-label" for="field_total_sugar">Tổng đường (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="glucose" id="field_glucose">
                                <label class="form-check-label" for="field_glucose">Glucose (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="fructose" id="field_fructose">
                                <label class="form-check-label" for="field_fructose">Fructose (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="sucrose" id="field_sucrose">
                                <label class="form-check-label" for="field_sucrose">Sucrose (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="lactose" id="field_lactose">
                                <label class="form-check-label" for="field_lactose">Lactose (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="maltose" id="field_maltose">
                                <label class="form-check-label" for="field_maltose">Maltose (g)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="purine" id="field_purine">
                                <label class="form-check-label" for="field_purine">Purine (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="phytosterol" id="field_phytosterol">
                                <label class="form-check-label" for="field_phytosterol">Phytosterol (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="lycopene" id="field_lycopene">
                                <label class="form-check-label" for="field_lycopene">Lycopene (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="b_carotene" id="field_b_carotene">
                                <label class="form-check-label" for="field_b_carotene">β-Carotene (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="a_carotene" id="field_a_carotene">
                                <label class="form-check-label" for="field_a_carotene">α-Carotene (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="b_cryptoxanthin" id="field_b_cryptoxanthin">
                                <label class="form-check-label" for="field_b_cryptoxanthin">β-Cryptoxanthin (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="lutein_zeaxanthin" id="field_lutein_zeaxanthin">
                                <label class="form-check-label" for="field_lutein_zeaxanthin">Lutein + Zeaxanthin (µg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="total_isoflavone" id="field_total_isoflavone">
                                <label class="form-check-label" for="field_total_isoflavone">Total Isoflavone (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="daidzein" id="field_daidzein">
                                <label class="form-check-label" for="field_daidzein">Daidzein (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="genistein" id="field_genistein">
                                <label class="form-check-label" for="field_genistein">Genistein (mg)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input export-field" type="checkbox" value="glycetin" id="field_glycetin">
                                <label class="form-check-label" for="field_glycetin">Glycetin (mg)</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-2 justify-content-center mt-2">
              <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button"  data-bs-dismiss="modal">Huỷ</button>
              </div>
              <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="confirmExportExcel()">Xuất Excel</button>
              </div>
            </div>
          </div>
        </div>
    </div>
    
    <script type="text/javascript">
        var dataTable;
        var listData = [];
        var researchId;
        var currentResearchId; // Để lưu research ID khi chọn fields
        
        $(document).ready(function () {
            dataTable = $('#dataTable').DataTable({
                dom: '<"top d-flex flex-wrap gap-2 justify-content-between align-items-center mb-2"pf>rt<"bottom d-flex flex-wrap gap-2 justify-content-between align-items-center mt-2"il><"clear">',
                serverSide: true,
                processing: true,
                responsive: true,
                paging: true,
                pageLength: 25,
                lengthMenu: [25, 50, 75, 100],
                ajax: {
                    url: '/research/list',
                    method: 'POST',
                    dataType: "json",
                    beforeSend: function() {
                        loading.show();
                    },
                    complete: function() {  // Thêm complete để ẩn loading khi xong
                        loading.hide();
                    },
                    dataSrc: function(response){
                        if (response.data) {
                            listData = response.data; // Assign data to listData
                            return response.data;  // Trả về mảng dữ liệu
                        } else {
                            listData = []; // Assign empty array if no data
                            return [];  // Trả về mảng rỗng nếu không có dữ liệu
                        }
                    },
                    data: function(n){
                        if (!n) {
                            n = {};
                        }
                    }
                },
                scrollX: true,
                rowId: function(row) {
                    return 'research-' + row.id; // Thêm "row-" vào trước giá trị id
                },
                columns: [
                    {   data: 'name',
                        orderable: true, // Cho phép sort
                        searchable: true, // Cho phép search
                        render: function(data, type, row) {
                            return `<a href='/research/detail/${row.id}'>${data}</a>`;
                        },
                        className: 'min-width-110'
                    },
                    {   data: 'note',
                        orderable: true, // Cho phép sort
                        searchable: true, // Cho phép search
                        className: 'min-width-110'
                    },
                    {
                        data: null,
                        orderable: false, // Không cho phép sort cột actions
                        searchable: false, // Không search trong cột actions
                        render: function (data, type, row) {
                            return `
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm btn-circle" data-id="${row.id}" onclick="exportExcel(${row.id})" title="Xuất Excel"><i class="fas fa-download"></i></button>
                                    <button class="btn btn-info btn-sm btn-circle" data-id="${row.id}" onclick="openEditResearch(${row.id})" title="Sửa"><i class="fas fa-pen-square"></i></button>
                                    <button class="btn btn-danger btn-sm btn-circle" data-id="${row.id}" onclick="deleteResearch(${row.id}, '${row.name}')" title="Xóa"><i class="fas fa-trash"></i></button>
                                </div>
                            `;
                        },
                    },
                ],
                // Cấu hình order mặc định DESC
                order: [], // Để trống để server xử lý order
                // Cấu hình searching
                searching: true, // Bật tính năng search
                // Cấu hình ordering
                ordering: true, // Bật tính năng sort
            });
        });

        function deleteResearch(id, name){
            confirmDialog('Xác nhận', 'Bạn có muốn xóa nghiên cứu ' + name).then(responseData =>{
                if(responseData.isConfirmed && id){
                    $.ajax({
                        type: 'POST',
                        url: '/research/active',
                        data: {id: id, active: 0},
                        beforeSend: function () {
                            loading.show();
                        },
                        success: function (result) {
                            loading.hide();
                            if (result.success) {
                                toarstMessage('Xóa nghiên cứu thành công');
                                document.getElementById('research-' + id).remove();
                            } else {
                                toarstError(result.message);
                            }
                        },
                        error: function (jqXHR, exception) {
                            loading.hide();
                            ajax_call_error(jqXHR, exception);
                        }
                    });
                }
            })
        }

        function addResearch(){
            let url = `/research/create`;
            
            let errors = [];
            let param = {
                name: $('#rs_name').val(),
                note: $('#note').val()
            };
            if(researchId){
                param['id'] = researchId;
                url = `/research/update`;
            } 
            if(errors.length > 0){
                return;
            } else {
                $.ajax({
                    type: 'POST',
                    url: url,
                    data: param,
                    beforeSend: function () {
                        loading.show();
                    },
                    success: function (result) {
                        loading.hide();
                        if (result.success) {
                            toarstMessage('Lưu thành công');
                            $('#modal-add-research').modal('hide');
                            $("#dataTable").DataTable().ajax.reload();
                        } else {
                            toarstError(result.message);
                        }
                    },
                    error: function (jqXHR, exception, error) {
                        loading.hide();
                        ajax_call_error(jqXHR, exception);
                    }
                });
            }
        }

        function openEditResearch(id){
            researchId = id;
            if(!listData || listData.length == 0){
                return;
            }
            for(let item of listData){
                if(item.id == id){
                    $('#note').val(item.note);
                    $('#rs_name').val(item.name);
                    break;
                }
            }
            $('#modal-add-research').modal('show');
        }

        function exportExcel(researchId){
            if (!researchId) {
                toarstError('Không tìm thấy ID nghiên cứu!');
                return;
            }
            
            currentResearchId = researchId;
            
            // Hiển thị modal chọn fields
            $('#modal-export-config').modal('show');
        }
        
        function selectAllFields() {
            $('.export-field').prop('checked', true);
        }
        
        function unselectAllFields() {
            $('.export-field').prop('checked', false);
        }
        
        function selectBasicFields() {
            $('.export-field').prop('checked', false);
            // Chọn các trường cơ bản
            $('#field_fullname, #field_menu_name, #field_energy, #field_protein, #field_lipid, #field_carbohydrate, #field_fiber, #field_calci, #field_fe, #field_zinc, #field_vitamin_c').prop('checked', true);
        }
        
        function confirmExportExcel() {
            // Kiểm tra currentResearchId
            if (!currentResearchId) {
                toarstError('Không tìm thấy ID nghiên cứu! Vui lòng thử lại.');
                return;
            }

            // Lấy danh sách các fields được chọn
            const selectedFields = [];
            $('.export-field:checked').each(function() {
                selectedFields.push($(this).val());
            });

            if (selectedFields.length === 0) {
                toarstError('Vui lòng chọn ít nhất 1 thông số để xuất!');
                return;
            }

            // Tìm tên nghiên cứu
            let researchName = 'Nghiên cứu';
            if (listData && listData.length > 0) {
                for(let item of listData){
                    if(item.id == currentResearchId){
                        researchName = item.name;
                        break;
                    }
                }
            }
            
            $('#modal-export-config').modal('hide');
            
            confirmDialog('Xác nhận', `Xuất Excel cho nghiên cứu "${researchName}" với ${selectedFields.length} thông số?`).then(responseData =>{
                if(responseData.isConfirmed){
                    // Gửi request với danh sách fields
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/research/export-excel/${currentResearchId}`;
                    form.target = '_blank';
                    
                    const fieldsInput = document.createElement('input');
                    fieldsInput.type = 'hidden';
                    fieldsInput.name = 'selectedFields';
                    fieldsInput.value = JSON.stringify(selectedFields);
                    
                    form.appendChild(fieldsInput);
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                }
            });
        }
    </script>
</body>
</html>
