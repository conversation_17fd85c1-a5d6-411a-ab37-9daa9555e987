<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../layout/head') %>
        <title>Admin</title>
</head>

<body>
    <!-- Page Wrapper -->
    <div id="wrapper">
        <%- include('../layout/sidebar') %>
            <!-- Content Wrapper -->
            <div id="content-wrapper" class="d-flex flex-column">
                <!-- Main Content -->
                <div id="content">
                    <%- include('../layout/header') %>
                    <%if(errors.length > 0){%>
                        <div class="container">
                            <div class="box mt-3">
                                <%for(let item of errors){%>
                                <div><%-JSON.stringify(item)%></div>
                                <%}%>
                            </div>
                        </div>
                    <%}else{%>
                        <div class="container-fluid">
                            <div class="card shadow mt-3">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">Danh sách User</h6>
                                    <a class="btn btn-success btn-circle" onclick="openModalCreateTable('user')">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Họ Tên</th>
                                                    <th>Email</th>
                                                    <th>Quyền</th>
                                                    <th>Kích hoạt</th>
                                                    <th>Thao tác</th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% } %>
                </div>
                <%- include('../layout/footer') %>
            </div>
    </div>
    <div class="modal fade" id="modal-add-user" tabindex="-1" aria-hidden="true">
        <input type="hidden" id="user_select_edit">
        <div class="modal-dialog modal-dialog-centered modal-xl">
          <div class="modal-content">
            <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            <h3 class="modal-title fs-6 text-uppercase text-center mb-3">Thêm tài khoản</h3>
            <div class="row flex-wrap g-3">
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Tên</h6>
                        </div>
                        <div class="card-body">
                            <input type="text" class="form-control" id="fullname" placeholder="Nhập họ tên">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Email</h6>
                        </div>
                        <div class="card-body">
                            <input type="email" class="form-control" id="email" placeholder="Email">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Số điện thoại</h6>
                        </div>
                        <div class="card-body">
                            <input type="text" class="form-control" id="phone" placeholder="Số điện thoại">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Mật khẩu</h6>
                        </div>
                        <div class="card-body">
                            <input type="password" class="form-control" id="password" placeholder="Password">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Giới tính</h6>
                        </div>
                        <div class="card-body">
                            <div data-plugin="virtual-select" data-config='{"placeholder":"Chọn giới tính"}' id="gender"
                                data-options='[{"label":"Nam","value":1},{"label":"Nữ","value":0},{"label":"Khác","value":2}]'></div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quyền</h6>
                        </div>
                        <div class="card-body">
                            <div data-plugin="virtual-select" data-config='{"placeholder":"Chọn quyền","multiple":true}' id="role"
                                data-options='[{"label":"Admin","value":1},{"label":"Customer","value":2},{"label":"Viêm gan","value":3},{"label":"Uốn ván","value":4},{"label":"Hội chẩn","value":5},{"label":"Viêm gan Mt1","value":6},{"label":"Đánh giá KPA","value":7},{"label":"Phiếu hội chẩn","value":8}]'></div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Chiến dịch</h6>
                        </div>
                        <div class="card-body">
                            <div data-plugin="virtual-select" data-config='{"placeholder":"Chọn chiến dịch"}' id="campaign"
                                data-options='<%=campaigns%>'></div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Kích hoạt</h6>
                        </div>
                        <div class="card-body">
                            <div data-plugin="virtual-select" data-config='{"placeholder":"Active"}' id="active"
                                data-options='[{"label":"Deactive","value":"0"},{"label":"Active","value":"1"}]'></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row g-2 justify-content-center mt-2">
              <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button"  data-bs-dismiss="modal">Huỷ</button>
              </div>
              <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="addDataTable('user')">Lưu</button>
              </div>
            </div>
          </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            dataTable = $('#dataTable').DataTable({
                dom: '<"top d-flex flex-wrap gap-2 justify-content-between align-items-center mb-2"pf>rt<"bottom d-flex flex-wrap gap-2 justify-content-between align-items-center mt-2"il><"clear">',
                serverSide: true,
                processing: true,
                responsive: true,
                pageLength: 25,
                lengthMenu: [25, 50, 75, 100],
                paging: true,
                scrollX: true,
                ajax: {
                    url: `/admin/user/list/`,
                    method: 'POST',
                    dataType: "json",
                    beforeSend: function() {
                        loading.show();
                    },
                    complete: function() {  // Thêm complete để ẩn loading khi xong
                        loading.hide();
                    },
                    dataSrc: function(response){
                        if (response.data) {
                            return response.data;  // Trả về mảng dữ liệu
                        } else {
                            return [];  // Trả về mảng rỗng nếu không có dữ liệu
                        }
                    },
                },
                rowId: function(row) {
                    return  'row-' + row.id; // Thêm "row-" vào trước giá trị id
                },
                columns: [
                    {
                        data: 'fullname',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'email',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'role',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row){
                            if (!data || !Array.isArray(data)) {
                                return '';
                            }

                            const roleLabels = {
                                1: 'Admin',
                                2: 'Khách hàng',
                                3: 'Viêm gan',
                                4: 'Uốn ván',
                                5: 'Hội chẩn',
                                6: 'Viêm gan Mt1',
                                7: 'Đánh giá KPA',
                                8: 'Phiếu hội chẩn'
                            };

                            return data.map(roleId => roleLabels[roleId] || `Role ${roleId}`).join(', ');
                        }
                    },
                    {
                        data: 'active',
                        orderable: true,
                        searchable: false,
                        render: function(data, type, row){
                            switch(data){
                                case 0: return 'Chưa kích hoạt';
                                case 1: return 'Hoạt động';
                                default: break;
                            }
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        render: function (data, type, row) {
                            return `
                                <div class="d-flex gap-2">
                                    <a class="btn btn-info btn-sm btn-circle" data-id="${row.id}" onclick="openModalEditTable('user', ${row.id})"><i class="fas fa-pen-square"></i></a>
                                    <button class="btn btn-danger btn-sm btn-circle" data-id="${row.id}" onclick="deleteUser(${row.id},'${row.fullname}')"><i class="fas fa-trash"></i></button>
                                </div>
                            `;
                        },
                    },
                ],
                // Cấu hình order mặc định DESC
                order: [], // Để trống để server xử lý order
                // Cấu hình searching
                searching: true, // Bật tính năng search
                // Cấu hình ordering
                ordering: true, // Bật tính năng sort
            });
        });

    </script>
</body>

</html>