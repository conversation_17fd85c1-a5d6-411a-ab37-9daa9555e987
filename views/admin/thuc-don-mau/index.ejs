<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../layout/head') %>
        <title>Chi tiết thực đơn mẫu - Patients</title>
</head>

<body>

    <!-- Page Wrapper -->
    <div id="wrapper">
        <%- include('../layout/sidebar') %>

            <!-- Content Wrapper -->
            <div id="content-wrapper" class="d-flex flex-column">
                <%- include('../layout/header') %>
                    <!-- Begin Page Content -->
                    <% if(errors.length> 0){%>
                        <div class="container">
                            <div class="box mt-3">
                                <%for(let item of errors){%>
                                    <div><%-JSON.stringify(item)%></div>
                                    <%}%>
                            </div>
                        </div>
                        <% }else{ %>
                            <div class="container-fluid">
                                <!-- Header với điều hướng -->
                                <div class="d-sm-flex align-items-center justify-content-between mb-4">
                                    <h1 class="h3 mb-0 text-gray-800">
                                        <% if(menuExamine && menuExamine.length > 0 && menuExamine[0].isExisting) { %>
                                            Chi tiết: <%= menuExamine[0].name %>
                                        <% } else { %>
                                            Thực đơn mẫu mới
                                        <% } %>
                                    </h1>
                                    <div class="d-flex">
                                        <a href="/admin/thuc-don-mau" class="btn btn-secondary btn-sm mr-2">
                                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Quay lại danh sách
                                        </a>
                                        <% if(!menuExamine || menuExamine.length === 0 || !menuExamine[0].isExisting) { %>
                                            <a href="/admin/thuc-don-mau/new" class="btn btn-success btn-sm">
                                                <i class="fas fa-plus fa-sm text-white-50"></i> Tạo mới
                                            </a>
                                        <% } %>
                                    </div>
                                </div>

                                <div class="card shadow mb-4">
                                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                        <div class="d-flex align-items-center flex-grow-1">
                                            <input type="text" class="form-control form-control-lg text-primary font-weight-bold border-0 bg-transparent" 
                                                   id="name_menu_text" 
                                                   placeholder="Nhập tên thực đơn"
                                                   value="<% if(menuExamine && menuExamine.length > 0 && menuExamine[0].name) { %><%= menuExamine[0].name %><% } else { %>Thực đơn mới<% } %>"
                                                   style="font-size: 1.25rem; padding: 0.25rem 0.5rem;">
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <% if(menuExamine && menuExamine.length > 0 && menuExamine[0].isExisting) { %>
                                                <button title="Cập nhật thực đơn" class="btn btn-warning btn-circle mr-2" onclick="showUpdateMenuNameModal()">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            <% } %>
                                            <button title="Lưu thực đơn" class="btn btn-success btn-circle" onclick="saveMenuExample()">
                                                <i class="fa-solid fa-floppy-disk"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body px-0">
                                        <div class="card shadow mb-4">
                                            <div class="card-header py-3">
                                                <h6 class="text-primary mb-0">Thêm thực phẩm</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-2 align-items-center">
                                                    <div class="col-12 col-md-4 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold mt-2"
                                                            for="menuTime_id">Chọn giờ ăn</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <div class="form-control-has-addon">
                                                                <div id="menuTime_id" data-plugin="virtual-select" data-config='{"placeholder":"Chọn giờ ăn"}'
                                                        data-options='<%=JSON.stringify(menuTime.map(s => ({label: s.name, value: s.id})))%>'></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Filter cho thực phẩm -->
                                                    <div class="col-12 col-md-4 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold" for="food_type">Loại thực phẩm</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <select id="food_type" name="food_type" class="form-control" onchange="updateFoodDropdown('food_name')">
                                                                <option value="">Tất cả</option>
                                                                <option value="raw">Sống</option>
                                                                <option value="cooked">Chín ĐP</option>
                                                                <option value="cooked_vdd">Chín VDD</option>
                                                                <option value="milk">Sữa</option>
                                                                <option value="ddd">Dịch DD</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-md-4 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold" for="food_year">Năm dữ liệu</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <select id="food_year" name="food_year" class="form-control" onchange="updateFoodDropdown('food_name')">
                                                                <option value="">Tất cả</option>
                                                                <option value="2000">2000</option>
                                                                <option value="2017">2017</option>
                                                                <option value="2025">2025</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row g-2 align-items-center">
                                                    <div class="col-12 col-md-8 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold mt-2"
                                                            for="food_name">Thực
                                                            phẩm</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <div class="form-control-has-addon">
                                                                <div id="food_name"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-md-4">
                                                        <div class="input-group">
                                                                <input type="number" class="form-control form-control-title p-1"
                                                                    id="weight_food" placeholder="Khối lượng"
                                                                    data-initial-value="0">
                                                                                                                <button class="btn btn-primary" type="button"
                                                    onclick="addFoodToMenuAdmin()"
                                                    title="Thêm thực phẩm vào thực đơn">Thêm</button>
                                                            </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                        
                                        <!-- Card chọn món ăn -->
                                        <div class="card shadow mb-4">
                                            <div class="card-header py-3">
                                                <h6 class="text-primary mb-0">Thêm món ăn vào thực đơn</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-2 align-items-center">
                                                    <div class="col-12 col-md-6 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold mt-2"
                                                            for="dish_menuTime_id">Chọn giờ ăn</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <div class="form-control-has-addon">
                                                                <div id="dish_menuTime_id" data-plugin="virtual-select" data-config='{"placeholder":"Chọn giờ ăn"}'
                                                        data-options='<%=JSON.stringify(menuTime.map(s => ({label: s.name, value: s.id})))%>'></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-2 align-items-center">
                                                    <div class="col-12 col-md-8 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold mt-2"
                                                            for="dish_name">Món ăn</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <div class="form-control-has-addon">
                                                                <div id="dish_name"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-md-4">
                                                        <div class="input-group">
                                                            <button class="btn btn-success" type="button"
                                                                onclick="addDishToMenu()"
                                                                title="Thêm món ăn vào thực đơn">Thêm món ăn</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Column Configuration Card -->
                                        <div class="card shadow mb-4" id="column-config-card" style="display: none;">
                                            <div class="card-header">
                                                <h6 class="m-0 font-weight-bold d-flex justify-content-between align-items-center">
                                                    <div class="d-flex align-items-center gap-2">
                                                        <i class="fas fa-columns"></i> Chọn cột hiển thị
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-primary float-right" onclick="toggleColumnSelector()">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                </h6>
                                            </div>
                                            <div class="card-body" id="column-selector">
                                                <!-- Column selector content will be generated by JavaScript -->
                                            </div>
                                        </div>

                                        <div class="card shadow mb-4">
                                            <div class="card-header py-3">
                                                <h6 class="text-primary mb-0">Thực đơn chi tiết</h6>
                                            </div>
                                            <div class="card-body px-0">
                                                <div class="row g-2 align-items-center">
                                                    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_menu"
                                                        style="width: 100%;display: none;">
                                                        <div class="table-responsive-inner px-3">
                                                            <table
                                                                class="table-2 table table-pe-x-3 mb-0 align-middle table-ds-booking table-bordered table-hover">
                                                                <thead class="text-center">
                                                                    <tr>
                                                                        <th class="text-center">Bữa ăn</th>
                                                                        <th class="text-center">Tên món ăn</th>
                                                                        <th class="text-center">Weight(g)</th>
                                                                        <th class="text-center">Energy(kcal)</th>
                                                                        <th class="text-center">Protein(g)</th>
                                                                        <th class="text-center">Fat(g)</th>
                                                                        <th class="text-center">Carbohydrate(g)</th>
                                                                        <th class="text-center">Thao tác</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>

                                                                </tbody>
                                                                <tfoot>
                                                                    <tr id="total-row" style="display: none;">
                                                                        <!-- Total row will be generated by JavaScript -->
                                                                    </tr>
                                                                </tfoot>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row g-2 align-items-center">
                                                    <div class="col-12 d-flex">
                                                        <label class="col-form-label col-md-auto fw-bold mt-2"
                                                            for="menu_example_note">Ghi chú</label>
                                                        <div class="col-form-body col-md w-100">
                                                            <div class="form-control-has-addon">
                                                                <textarea class="form-control" id="menu_example_note"
                                                                    placeholder="Ghi chú"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                <% } %>
                <%- include('../layout/footer') %>
            </div>
    </div>
    
    <script src="/js/menuExample.js?version=*******"></script>

    <script>
        // Khai báo global variables
        window.listMenuTime = <%- JSON.stringify(menuTime) %>;
        window.menuExamine = <%- JSON.stringify(menuExamine) %>;
        
        // Khởi tạo admin menu example
        $(document).ready(function() {
            initAdminMenuExample();
            generateFoodName("food_name");
            
            // Tạo column selector
            createColumnSelector();
            
            // Load cấu hình cột mặc định cho admin (không có patient_id)
            currentDisplayConfig = {
                visible_columns: ["weight", "energy", "protein", "fat", "carbohydrate"]
            };
            
            // Cập nhật header với cấu hình mặc định
            updateTableHeaderWithConfig();
        });
    </script>

    <!-- Modal xác nhận tạo mới -->
    <div class="modal fade" id="modal-cf-save-menu" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal"
                    aria-label="Close"></button>
                <h4 class="modal-title text-center mb-2">Bạn muốn tạo mới thực đơn mẫu không ?</h4>
                <p class="mb-5 fw-5 text-body-2 text-center">Nếu chọn không sẽ lưu vào thực đơn mẫu đang chọn.</p>
                <div class="row g-2 justify-content-center">
                    <div class="col-6">
                        <button class="btn btn-cancel w-100 text-uppercase" type="button"
                            data-bs-dismiss="modal">Không</button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-primary w-100 text-uppercase" type="button" onclick="saveMenuExample()">Có</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>

</html>