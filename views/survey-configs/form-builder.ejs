<!DOCTYPE html>
<html lang="en">
<head>
    <%- include('../layout/head') %>
    <title>Form Builder - <%= surveyConfig.name %></title>
    
    <!-- Form Builder styles -->
    <link href="/css/form-builder.css" rel="stylesheet">
    
    <!-- Additional icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- Page Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        <%- include('../layout/sidebar') %>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <%- include('../layout/header') %>
                
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <div class="content-wrapper">
                        <div class="content-header">
                            <div class="container-fluid">
                                <div class="row mb-2">
                                    <div class="col-sm-6">
                                        <h1 class="m-0">
                                            <i class="fas fa-tools"></i> Form Builder: <%= surveyConfig.name %>
                                        </h1>
                                    </div>
                                    <div class="col-sm-6">
                                        <ol class="breadcrumb float-sm-right">
                                            <li class="breadcrumb-item"><a href="/projects">Dự án</a></li>
                                            <li class="breadcrumb-item"><a href="/projects/<%= project.id %>/surveys">Khảo sát</a></li>
                                            <li class="breadcrumb-item active">Form Builder</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Toolbar -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body py-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-secondary" id="undoBtn" disabled>
                                                    <i class="fas fa-undo"></i> Undo
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" id="redoBtn" disabled>
                                                    <i class="fas fa-redo"></i> Redo
                                                </button>
                                            </div>
                                            
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-primary" id="previewBtn">
                                                    <i class="fas fa-eye"></i> Preview
                                                </button>
                                                <button type="button" class="btn btn-outline-info" id="importBtn">
                                                    <i class="fas fa-upload"></i> Import
                                                </button>
                                                <button type="button" class="btn btn-outline-success" id="exportBtn">
                                                    <i class="fas fa-download"></i> Export
                                                </button>
                                            </div>
                                            
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-primary" id="saveBtn">
                                                    <i class="fas fa-save"></i> Save Form
                                                </button>
                                                <button type="button" class="btn btn-success" id="publishBtn">
                                                    <i class="fas fa-globe"></i> Publish
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Builder Container -->
                        <div class="row">
                            <div class="col-12">
                                <div id="form-builder-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <%- include('../layout/footer') %>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-eye"></i> Form Preview
                    </h4>
                    <button type="button" class="close" data-bs-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><%= surveyConfig.name %></h5>
                                    <% if (surveyConfig.description) { %>
                                        <p class="text-muted mb-0"><%= surveyConfig.description %></p>
                                    <% } %>
                                </div>
                                <div class="card-body" id="previewFormContent">
                                    <!-- Preview content will be loaded here -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-mobile-alt"></i> Mobile Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mobile-preview" id="mobilePreview">
                                        <!-- Mobile preview content -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="testFormBtn">
                        <i class="fas fa-play"></i> Test Form
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-upload"></i> Import Form Configuration
                    </h4>
                    <button type="button" class="close" data-bs-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Upload JSON File:</label>
                        <input type="file" class="form-control" id="importFile" accept=".json">
                    </div>
                    <div class="form-group">
                        <label>Or paste JSON configuration:</label>
                        <textarea class="form-control" id="importJson" rows="10" placeholder="Paste JSON configuration here..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="importConfirmBtn">
                        <i class="fas fa-upload"></i> Import
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2">Saving form...</p>
        </div>
    </div>

    <!-- Form Builder JavaScript -->
    <script src="/js/form-builder.js"></script>
    
    <script>
        let formBuilder;
        let currentConfig = null;
        
        $(document).ready(function() {
            // Initialize form builder
            formBuilder = new FormBuilder('form-builder-container');
            
            // Load existing configuration if available
            loadExistingConfig();
            
            // Bind toolbar events
            bindToolbarEvents();
            
            // Auto-save every 30 seconds
            setInterval(autoSave, 30000);
        });
        
        function loadExistingConfig() {
            // Load existing form configuration from server
            $.get(`/survey-configs/<%= surveyConfig.id %>/form-config`)
                .done(function(response) {
                    if (response.success && response.data) {
                        currentConfig = response.data;
                        formBuilder.importForm(currentConfig);
                    }
                })
                .fail(function() {
                    console.log('No existing configuration found');
                });
        }
        
        function bindToolbarEvents() {
            // Save button
            $('#saveBtn').click(function() {
                saveForm();
            });
            
            // Preview button
            $('#previewBtn').click(function() {
                showPreview();
            });
            
            // Export button
            $('#exportBtn').click(function() {
                exportForm();
            });
            
            // Import button
            $('#importBtn').click(function() {
                $('#importModal').modal('show');
            });
            
            // Import confirm
            $('#importConfirmBtn').click(function() {
                importForm();
            });
            
            // Publish button
            $('#publishBtn').click(function() {
                publishForm();
            });
            
            // Test form button
            $('#testFormBtn').click(function() {
                const url = `/survey/<%= surveyConfig.survey_url_slug %>`;
                window.open(url, '_blank');
            });
        }
        
        function saveForm() {
            const config = formBuilder.exportForm();
            
            $('#loadingOverlay').removeClass('d-none');
            
            $.ajax({
                url: `/survey-configs/<%= surveyConfig.id %>/save-form-config`,
                type: 'POST',
                data: {
                    config: JSON.stringify(config)
                },
                success: function(response) {
                    $('#loadingOverlay').addClass('d-none');
                    
                    if (response.success) {
                        currentConfig = config;
                        Swal.fire({
                            icon: 'success',
                            title: 'Saved!',
                            text: 'Form configuration saved successfully',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to save form'
                        });
                    }
                },
                error: function() {
                    $('#loadingOverlay').addClass('d-none');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to save form configuration'
                    });
                }
            });
        }
        
        function autoSave() {
            const config = formBuilder.exportForm();
            if (JSON.stringify(config) !== JSON.stringify(currentConfig)) {
                console.log('Auto-saving form...');
                saveForm();
            }
        }
        
        function showPreview() {
            const config = formBuilder.exportForm();
            
            // Generate preview HTML
            let previewHtml = '';
            config.fields.forEach(field => {
                const fieldConfig = formBuilder.fieldTypes[field.type];
                previewHtml += `<div class="mb-3">${fieldConfig.template.call(formBuilder, field, true)}</div>`;
            });
            
            previewHtml += `
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Submit Survey</button>
                </div>
            `;
            
            $('#previewFormContent').html(previewHtml);
            $('#mobilePreview').html(`<div class="mobile-frame">${previewHtml}</div>`);
            $('#previewModal').modal('show');
        }
        
        function exportForm() {
            const config = formBuilder.exportForm();
            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `<%= surveyConfig.name.replace(/[^a-zA-Z0-9]/g, '_') %>_form_config.json`;
            link.click();
        }
        
        function importForm() {
            let configData = null;
            
            // Try to get from file first
            const fileInput = document.getElementById('importFile');
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        configData = JSON.parse(e.target.result);
                        formBuilder.importForm(configData);
                        $('#importModal').modal('hide');
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'Imported!',
                            text: 'Form configuration imported successfully',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } catch (error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Invalid JSON format'
                        });
                    }
                };
                reader.readAsText(file);
            } else {
                // Try to get from textarea
                const jsonText = $('#importJson').val().trim();
                if (jsonText) {
                    try {
                        configData = JSON.parse(jsonText);
                        formBuilder.importForm(configData);
                        $('#importModal').modal('hide');
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'Imported!',
                            text: 'Form configuration imported successfully',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } catch (error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Invalid JSON format'
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'No Data',
                        text: 'Please select a file or paste JSON configuration'
                    });
                }
            }
        }
        
        function publishForm() {
            Swal.fire({
                title: 'Publish Form?',
                text: 'This will make the form available to the public. Continue?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, publish it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Save first, then publish
                    saveForm();
                    
                    setTimeout(() => {
                        const url = `/survey/<%= surveyConfig.survey_url_slug %>`;
                        Swal.fire({
                            icon: 'success',
                            title: 'Published!',
                            html: `Form is now live at:<br><a href="${url}" target="_blank">${url}</a>`,
                            confirmButtonText: 'Open Form'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.open(url, '_blank');
                            }
                        });
                    }, 1000);
                }
            });
        }
    </script>
    
    <style>
        #loadingOverlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
        }
        
        .mobile-frame {
            max-width: 300px;
            margin: 0 auto;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            font-size: 14px;
        }
        
        .mobile-frame .form-control {
            font-size: 14px;
            padding: 6px 10px;
        }
        
        .mobile-frame .btn {
            font-size: 14px;
            padding: 8px 16px;
        }
    </style>
</body>
</html>
