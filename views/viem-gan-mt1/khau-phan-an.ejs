<!DOCTYPE html>
<html lang="en">
<head>
    <%- include('../layout/head') %>
    <title>Bệnh nhân viêm gan MT1 - Khẩu phần ăn</title>
    <style>
        .min-width-150{min-width:150px}
        .min-width-200{min-width:200px}
    </style>
    <input type="hidden" id="type" value="<%=type%>">
    <input type="hidden" id="patient_id" value="<%=patient.id%>">
</head>

<body>

    <!-- Page Wrapper -->
   <div id="wrapper">
       <%- include('../layout/sidebar') %>

        <!-- Content Wrapper -->
       <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <%- include('../layout/header') %>
                <%if(errors.length > 0){%>
                    <div class="container">
                        <div class="box mt-3">
                            <%for(let item of errors){%>
                            <div><%-JSON.stringify(item)%></div>
                            <%}%>
                        </div>
                    </div>
                <%}else{%>
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <%- include('../layout/thong-tin-co-ban.ejs')%>

                    <div class="d-flex mt-3 gap-4">
                        <div class="flex-fill form-data card shadow" name="form-data">
                            <%- include('./module/menu.ejs')%>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>Ngày</th>
                                                <th>ND đường tiêu hóa (Sáng)</th>
                                                <th>ND đường tiêu hóa (Trưa)</th>
                                                <th>ND đường tiêu hóa (Tối)</th>
                                                <th>ND đường tiêu hóa (Bữa phụ)</th>
                                                <th>ND tĩnh mạch</th>
                                                <th>Ghi chú</th>
                                                <th>Thao tác</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <% } %>
            </div>
           <%- include('../layout/footer') %>
        </div>
   </div>

   <div class="modal fade" id="modal-add-broading" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
      <div class="modal-content">
        <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
        <h3 class="modal-title fs-6 text-uppercase text-center mb-3">Thêm theo dõi nội trú</h3>
        <div class="row flex-wrap g-3 card-body">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Ngày</h6>
                    </div>
                    <div class="card-body">
                        <div class="flatpickr flatpickr-input" data-plugin="flatpickr" id="ngay"
                                data-options='{"mode":"single", "allowInput": true}'>
                            <input class="form-control" type="text" placeholder="Ngày" id="ngay_input"
                                    data-input="data-input" autoComplete="off"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ND đường tiêu hóa (Sáng)</h6>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="nd_duong_th_sang" placeholder="ND đường tiêu hóa (Sáng)" rows="2"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ND đường tiêu hóa (Trưa)</h6>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="nd_duong_th_trua" placeholder="ND đường tiêu hóa (Trưa)" rows="2"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ND đường tiêu hóa (Tối)</h6>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="nd_duong_th_toi" placeholder="ND đường tiêu hóa (Tối)" rows="2"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ND đường tiêu hóa (Bữa phụ)</h6>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="nd_duong_th_bua_phu" placeholder="ND đường tiêu hóa (Bữa phụ)" rows="2"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">ND tĩnh mạch</h6>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="nd_tinh_mac" placeholder="ND tĩnh mạch" rows="3"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Ghi chú</h6>
                    </div>
                    <div class="card-body">
                        <input type="text" class="form-control" id="note" placeholder="Ghi chú">
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2 justify-content-center mt-2">
          <div class="col-6 col-md-auto">
            <button class="btn btn-cancel box-btn w-100 text-uppercase" data-bs-dismiss="modal" type="button">Huỷ</button>
          </div>
          <div class="col-6 col-md-auto">
            <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="addBroadingMt1(<%=patient.id%>,'<%=type%>')">Lưu</button>
          </div>
        </div>
      </div>
    </div>
    </div>
    <script src="/js/viem-gan-mt1.js?version=*******"></script>
   <script type="text/javascript">
    var dataTable;
    var boardingId, ngay_fp;
    const id = '<%=patient.id%>';
    const type = '<%=type%>';
    $(document).ready(function () {
        if(document.getElementById('ngay_input')){
            ngay_fp = document.querySelector('#ngay')._flatpickr;
        }
        dataTable = $('#dataTable').DataTable({
            dom: '<"top d-flex flex-wrap gap-2 justify-content-between align-items-center mb-2"pf>rt<"bottom d-flex flex-wrap gap-2 justify-content-between align-items-center mt-2"il><"clear">',
            serverSide: true,
            processing: true,
            responsive: true,
            pageLength: 25,
            lengthMenu: [25, 50, 75, 100],
            paging: true,
            scrollX: true,
            ajax: {
                url: `/viem-gan-mt1-list/${id}/${type}`,
                method: 'POST',
                dataType: 'json',
                beforeSend: function(){ loading.show(); },
                complete: function(){ loading.hide(); },
                dataSrc: function(response){ return response.data ? response.data : []; }
            },
            rowId: function(row){ return type + '-' + row.id; },
            initComplete: function (settings, json) {},
            columns: [
                { data: 'time', orderable: true, searchable: true, render: function(data){ return moment(data).format('D/M/YYYY'); } },
                { data: 'nd_duong_th_sang', orderable: true, searchable: true },
                { data: 'nd_duong_th_trua', orderable: true, searchable: true },
                { data: 'nd_duong_th_toi', orderable: true, searchable: true },
                { data: 'nd_duong_th_bua_phu', orderable: true, searchable: true },
                { data: 'nd_tinh_mac', orderable: true, searchable: true },
                { data: 'note', orderable: true, searchable: true, className: 'min-width-200' },
                { data: null, orderable: false, searchable: false, render: function (data, type, row) {
                        return `
                            <div class="d-flex gap-2">
                                <button class="btn btn-info btn-sm btn-circle" data-id="${row.id}" onclick="openModalEditBoardingMt1(${row.id}, '<%=type%>')"><i class="fas fa-pen-square"></i></button>
                                <button class="btn btn-danger btn-sm btn-circle" data-id="${row.id}" onclick="deleteBoardingMt1(${row.id}, '<%=type%>','${row.time}')"><i class="fas fa-trash"></i></button>
                            </div>
                        `;
                    }
                }
            ]
        });
    });
   </script>
</body>
</html>


