// File test để kiểm tra JWT authentication
const jwtService = require('../services/jwtService');
const commonService = require('../services/commonService');

async function testJWT() {
    console.log('=== Testing JWT Authentication ===');
    
    // Test 1: Tạo token
    console.log('\n1. Testing token creation...');
    const testUser = {
        id: 1,
        email: '<EMAIL>',
        fullname: 'Test User'
    };
    
    const { token, tokenId } = jwtService.createToken(testUser);
    console.log('Token created:', token ? 'SUCCESS' : 'FAILED');
    console.log('Token ID:', tokenId);
    
    // Test 2: Verify token
    console.log('\n2. Testing token verification...');
    const decoded = jwtService.verifyToken(token);
    console.log('Token verified:', decoded ? 'SUCCESS' : 'FAILED');
    if (decoded) {
        console.log('Decoded payload:', {
            id: decoded.id,
            email: decoded.email,
            tokenId: decoded.tokenId
        });
    }
    
    // Test 3: Get device info
    console.log('\n3. Testing device info...');
    const mockReq = {
        headers: {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        ip: '127.0.0.1'
    };
    
    const deviceInfo = jwtService.getDeviceInfo(mockReq);
    console.log('Device info:', deviceInfo);
    
    // Test 4: Refresh token
    console.log('\n4. Testing token refresh...');
    const newToken = jwtService.refreshTokenIfNeeded(decoded);
    console.log('Token refresh:', newToken ? 'NEEDED' : 'NOT NEEDED');
    
    console.log('\n=== JWT Test Completed ===');
}

// Chạy test
testJWT().catch(console.error); 