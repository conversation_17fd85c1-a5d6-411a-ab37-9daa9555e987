/**
 * Simple Google Sheets Test
 */

console.log('🧪 Testing Google Sheets Service...');

async function testCreateSheet() {
    try {
        const googleSheetsService = require('../services/googleSheetsService');
        
        console.log('1. Testing createNewSheet...');
        
        const result = await googleSheetsService.createNewSheet('Test Project Simple');
        
        console.log('Result:', result);
        
        if (result.sheetId) {
            console.log('✅ Sheet created successfully!');
            console.log('Sheet ID:', result.sheetId);
            console.log('Sheet URL:', result.sheetUrl);
        } else {
            console.log('⚠️ Sheet creation returned null (likely no credentials)');
        }
        
    } catch (error) {
        console.log('❌ Error:', error.message);
        console.log('Stack:', error.stack);
    }
}

testCreateSheet();
