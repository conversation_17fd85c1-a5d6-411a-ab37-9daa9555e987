/**
 * Test Suite for Survey System
 * Tests the complete flow from project creation to survey submission
 */

const axios = require('axios');
const assert = require('assert');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    email: '<EMAIL>',
    password: 'testpassword'
};

let authToken = null;
let testProjectId = null;
let testSurveyConfigId = null;
let testSurveySlug = null;

/**
 * Test Suite
 */
describe('Survey System Tests', function() {
    this.timeout(10000);

    before(async function() {
        console.log('Setting up test environment...');
        // Login to get auth token
        try {
            const loginResponse = await axios.post(`${BASE_URL}/login`, TEST_USER);
            authToken = loginResponse.data.token;
            console.log('✓ Authentication successful');
        } catch (error) {
            console.log('⚠ Authentication failed, tests may not work properly');
        }
    });

    describe('Project Management', function() {
        it('should create a new project', async function() {
            const projectData = {
                name: `Test Project ${Date.now()}`,
                description: 'This is a test project for automated testing',
                status: 1,
                start_date: '2025-01-01',
                end_date: '2025-12-31'
            };

            try {
                const response = await axios.post(`${BASE_URL}/projects/create`, projectData, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });

                assert.strictEqual(response.data.success, true);
                assert(response.data.data.id);
                testProjectId = response.data.data.id;
                console.log(`✓ Project created with ID: ${testProjectId}`);
            } catch (error) {
                console.log('✗ Project creation failed:', error.response?.data?.message || error.message);
                throw error;
            }
        });

        it('should retrieve project list', async function() {
            try {
                const response = await axios.post(`${BASE_URL}/projects/list`, {}, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });

                assert.strictEqual(response.data.success, true);
                assert(Array.isArray(response.data.data.data));
                console.log(`✓ Retrieved ${response.data.data.data.length} projects`);
            } catch (error) {
                console.log('✗ Project list retrieval failed:', error.response?.data?.message || error.message);
                throw error;
            }
        });

        it('should update project', async function() {
            if (!testProjectId) {
                this.skip();
                return;
            }

            const updateData = {
                id: testProjectId,
                name: `Updated Test Project ${Date.now()}`,
                description: 'Updated description for test project',
                status: 1
            };

            try {
                const response = await axios.post(`${BASE_URL}/projects/update`, updateData, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });

                assert.strictEqual(response.data.success, true);
                console.log('✓ Project updated successfully');
            } catch (error) {
                console.log('✗ Project update failed:', error.response?.data?.message || error.message);
                throw error;
            }
        });
    });

    describe('Survey Configuration', function() {
        it('should create a survey configuration', async function() {
            if (!testProjectId) {
                this.skip();
                return;
            }

            testSurveySlug = `test-survey-${Date.now()}`;
            const surveyData = {
                project_id: testProjectId,
                name: 'Test Survey',
                description: 'This is a test survey configuration',
                survey_url_slug: testSurveySlug,
                is_active: 1,
                allow_multiple_responses: 0,
                require_email: 1,
                success_message: 'Thank you for participating in our test survey!'
            };

            try {
                const response = await axios.post(`${BASE_URL}/survey-configs/create`, surveyData, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });

                assert.strictEqual(response.data.success, true);
                assert(response.data.data.id);
                testSurveyConfigId = response.data.data.id;
                console.log(`✓ Survey config created with ID: ${testSurveyConfigId}`);
            } catch (error) {
                console.log('✗ Survey config creation failed:', error.response?.data?.message || error.message);
                throw error;
            }
        });

        it('should configure survey fields', async function() {
            if (!testSurveyConfigId) {
                this.skip();
                return;
            }

            const fieldsData = {
                survey_config_id: testSurveyConfigId,
                fields: [
                    {
                        field_name: 'full_name',
                        field_label: 'Họ và tên',
                        field_type: 'text',
                        is_required: true,
                        placeholder: 'Nhập họ và tên của bạn'
                    },
                    {
                        field_name: 'email',
                        field_label: 'Email',
                        field_type: 'email',
                        is_required: true,
                        placeholder: '<EMAIL>'
                    },
                    {
                        field_name: 'age_group',
                        field_label: 'Nhóm tuổi',
                        field_type: 'select',
                        is_required: true,
                        field_options: [
                            { value: '18-25', label: '18-25 tuổi' },
                            { value: '26-35', label: '26-35 tuổi' },
                            { value: '36-45', label: '36-45 tuổi' },
                            { value: '46+', label: 'Trên 45 tuổi' }
                        ]
                    },
                    {
                        field_name: 'interests',
                        field_label: 'Sở thích',
                        field_type: 'multiselect',
                        is_required: false,
                        field_options: [
                            { value: 'sports', label: 'Thể thao' },
                            { value: 'music', label: 'Âm nhạc' },
                            { value: 'reading', label: 'Đọc sách' },
                            { value: 'travel', label: 'Du lịch' }
                        ]
                    },
                    {
                        field_name: 'feedback',
                        field_label: 'Góp ý',
                        field_type: 'textarea',
                        is_required: false,
                        placeholder: 'Chia sẻ góp ý của bạn...'
                    }
                ]
            };

            try {
                const response = await axios.post(`${BASE_URL}/survey-configs/save-fields`, fieldsData, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });

                assert.strictEqual(response.data.success, true);
                console.log('✓ Survey fields configured successfully');
            } catch (error) {
                console.log('✗ Survey fields configuration failed:', error.response?.data?.message || error.message);
                throw error;
            }
        });
    });

    describe('Public Survey', function() {
        it('should load public survey form', async function() {
            if (!testSurveySlug) {
                this.skip();
                return;
            }

            try {
                const response = await axios.get(`${BASE_URL}/survey/${testSurveySlug}`);
                assert.strictEqual(response.status, 200);
                assert(response.data.includes('Test Survey'));
                console.log('✓ Public survey form loaded successfully');
            } catch (error) {
                console.log('✗ Public survey form loading failed:', error.response?.status || error.message);
                throw error;
            }
        });

        it('should submit survey response', async function() {
            if (!testSurveySlug) {
                this.skip();
                return;
            }

            const responseData = {
                full_name: 'Test User',
                email: '<EMAIL>',
                age_group: '26-35',
                interests: ['sports', 'music'],
                feedback: 'This is a test feedback for automated testing.'
            };

            try {
                const response = await axios.post(`${BASE_URL}/survey/${testSurveySlug}/submit`, responseData);
                assert.strictEqual(response.data.success, true);
                assert(response.data.data.responseId);
                console.log(`✓ Survey response submitted with ID: ${response.data.data.responseId}`);
            } catch (error) {
                console.log('✗ Survey response submission failed:', error.response?.data?.message || error.message);
                throw error;
            }
        });

        it('should validate required fields', async function() {
            if (!testSurveySlug) {
                this.skip();
                return;
            }

            const incompleteData = {
                // Missing required fields: full_name, email, age_group
                feedback: 'This should fail validation'
            };

            try {
                const response = await axios.post(`${BASE_URL}/survey/${testSurveySlug}/submit`, incompleteData);
                assert.strictEqual(response.data.success, false);
                console.log('✓ Required field validation working correctly');
            } catch (error) {
                // This is expected for validation errors
                console.log('✓ Required field validation working correctly');
            }
        });
    });

    describe('Google Sheets Integration', function() {
        it('should test Google Sheets service', async function() {
            // This test requires Google Sheets API credentials
            // Skip if not configured
            if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL) {
                console.log('⚠ Skipping Google Sheets test - credentials not configured');
                this.skip();
                return;
            }

            try {
                const googleSheetsService = require('../services/googleSheetsService');
                
                // Test creating a new sheet
                const result = await googleSheetsService.createNewSheet('Test Sheet', ['Name', 'Email', 'Response']);
                assert(result.sheetId);
                assert(result.sheetUrl);
                console.log('✓ Google Sheets integration working');
            } catch (error) {
                console.log('✗ Google Sheets integration failed:', error.message);
                // Don't throw error as this might be due to API limits or credentials
            }
        });
    });

    after(async function() {
        console.log('Cleaning up test data...');
        
        // Clean up test project
        if (testProjectId && authToken) {
            try {
                await axios.post(`${BASE_URL}/projects/${testProjectId}/delete`, {}, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });
                console.log('✓ Test project cleaned up');
            } catch (error) {
                console.log('⚠ Failed to clean up test project');
            }
        }
        
        console.log('Test cleanup completed');
    });
});

/**
 * Manual Test Functions
 * These can be run individually for debugging
 */

async function testProjectCreation() {
    console.log('Testing project creation...');
    // Implementation here
}

async function testSurveyConfiguration() {
    console.log('Testing survey configuration...');
    // Implementation here
}

async function testPublicSurveySubmission() {
    console.log('Testing public survey submission...');
    // Implementation here
}

// Export for manual testing
module.exports = {
    testProjectCreation,
    testSurveyConfiguration,
    testPublicSurveySubmission
};

// Run tests if this file is executed directly
if (require.main === module) {
    console.log('Running Survey System Tests...');
    console.log('Make sure the server is running on http://localhost:3000');
    console.log('And you have proper test user credentials configured');
}
