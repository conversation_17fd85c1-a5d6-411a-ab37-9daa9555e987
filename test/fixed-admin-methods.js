// Fixed methods cho adminController.js
// Copy các method này vào adminController.js để thay thế các method bị lỗi

const fixedMethods = {
    auditLogsList: `
    auditLogsList: (req, res) => {
        try {
            const dataTableService = require('../services/dataTableService');
            
            if (!req.user.isAdmin) {
                return res.json(dataTableService.createErrorResponse(req.body, 'Bạn không có quyền truy cập danh sách này!'));
            }
            
            // Cấu hình DataTable
            const config = {
                table: 'audit_logs',
                columns: ['id', 'user_id', 'email', 'action', 'resource', 'ip_address', 'created_at'],
                primaryKey: 'id',
                filters: {},
                searchColumns: ['email', 'action', 'resource', 'ip_address'],
                columnsMapping: [
                    '', // column 0 - actions
                    'id', // column 1
                    'user_id', // column 2
                    'email', // column 3
                    'action', // column 4
                    'resource', // column 5
                    'ip_address', // column 6
                    'created_at' // column 7
                ],
                defaultOrder: [
                    { column: 'id', dir: 'DESC' }
                ],
                checkRole: false
            };

            // Xử lý request
            dataTableService.handleDataTableRequest(req, res, config);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    },`,

    authLogsList: `
    authLogsList: (req, res) => {
        try {
            const dataTableService = require('../services/dataTableService');
            
            if (!req.user.isAdmin) {
                return res.json(dataTableService.createErrorResponse(req.body, 'Bạn không có quyền truy cập danh sách này!'));
            }
            
            // Cấu hình DataTable
            const config = {
                table: 'auth_logs',
                columns: ['id', 'email', 'action', 'success', 'ip_address', 'created_at'],
                primaryKey: 'id',
                filters: {},
                searchColumns: ['email', 'action', 'ip_address'],
                columnsMapping: [
                    '', // column 0 - actions
                    'id', // column 1
                    'email', // column 2
                    'action', // column 3
                    'success', // column 4
                    'ip_address', // column 5
                    'created_at' // column 6
                ],
                defaultOrder: [
                    { column: 'id', dir: 'DESC' }
                ],
                checkRole: false
            };

            // Xử lý request
            dataTableService.handleDataTableRequest(req, res, config);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    },`,

    activityLogsList: `
    activityLogsList: (req, res) => {
        try {
            const dataTableService = require('../services/dataTableService');
            
            if (!req.user.isAdmin) {
                return res.json(dataTableService.createErrorResponse(req.body, 'Bạn không có quyền truy cập danh sách này!'));
            }
            
            // Cấu hình DataTable
            const config = {
                table: 'activity_logs',
                columns: ['id', 'user_id', 'action', 'resource', 'details', 'ip_address', 'created_at'],
                primaryKey: 'id',
                filters: {},
                searchColumns: ['action', 'resource', 'details', 'ip_address'],
                columnsMapping: [
                    '', // column 0 - actions
                    'id', // column 1
                    'user_id', // column 2
                    'action', // column 3
                    'resource', // column 4
                    'details', // column 5
                    'ip_address', // column 6
                    'created_at' // column 7
                ],
                defaultOrder: [
                    { column: 'id', dir: 'DESC' }
                ],
                checkRole: false
            };

            // Xử lý request
            dataTableService.handleDataTableRequest(req, res, config);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    }`
};

console.log('Fixed methods ready to copy:');
console.log('1. auditLogsList');
console.log('2. authLogsList'); 
console.log('3. activityLogsList');
console.log('\nCopy these methods to replace the broken ones in adminController.js');

module.exports = fixedMethods;
