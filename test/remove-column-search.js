// Script để xóa column search khỏi tất cả DataTable views
const fs = require('fs');
const path = require('path');

const viewsToClean = [
    'views/research/listPatient.ejs',
    'views/patient/list.ejs',
    'views/admin/user/list.ejs',
    'views/admin/campaign/list.ejs',
    'views/admin/admin-logs/index.ejs',
    'views/cat-gan-nho/index.ejs',
    'views/uon-van/khau-phan-an.ejs'
];

function removeColumnSearchFromView(filePath) {
    console.log(`\n🧹 Cleaning ${filePath}...`);
    
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  File not found: ${filePath}`);
            return false;
        }
        
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        
        // Pattern 1: Xóa toàn bộ initComplete function với column search
        const initCompletePattern = /\/\/ Thêm tìm kiếm theo từng cột với các tính năng cải tiến[\s\S]*?initComplete:\s*function\s*\([^)]*\)\s*\{[\s\S]*?\}\s*,?\s*(?=\}|\w+:)/g;
        if (initCompletePattern.test(content)) {
            content = content.replace(initCompletePattern, '');
            modified = true;
            console.log('✅ Removed initComplete with column search');
        }
        
        // Pattern 2: Xóa initComplete function đơn giản hơn
        const simpleInitPattern = /initComplete:\s*function\s*\([^)]*\)\s*\{[\s\S]*?api\.columns\(\)\.search[\s\S]*?\}\s*,?\s*/g;
        if (simpleInitPattern.test(content)) {
            content = content.replace(simpleInitPattern, '');
            modified = true;
            console.log('✅ Removed simple initComplete');
        }
        
        // Pattern 3: Xóa CSS cho filters
        const filtersCSSPattern = /\/\* Advanced search styles \*\/[\s\S]*?#clearAllFilters\s*\{[\s\S]*?\}/g;
        if (filtersCSSPattern.test(content)) {
            content = content.replace(filtersCSSPattern, '');
            modified = true;
            console.log('✅ Removed filters CSS');
        }
        
        // Pattern 4: Xóa CSS filters khác
        const otherFiltersCSSPattern = /\.filters th[\s\S]*?\}/g;
        if (otherFiltersCSSPattern.test(content)) {
            content = content.replace(otherFiltersCSSPattern, '');
            modified = true;
            console.log('✅ Removed other filters CSS');
        }
        
        // Pattern 5: Xóa comment về column search
        const commentPattern = /\/\/ Thêm tìm kiếm theo từng cột[\s\S]*?(?=\w+:|$)/g;
        if (commentPattern.test(content)) {
            content = content.replace(commentPattern, '');
            modified = true;
            console.log('✅ Removed column search comments');
        }
        
        // Pattern 6: Sửa trailing commas
        content = content.replace(/,(\s*)\}/g, '$1}');
        
        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ Successfully cleaned ${filePath}`);
            return true;
        } else {
            console.log(`⚠️  No column search found in ${filePath}`);
            return false;
        }
        
    } catch (error) {
        console.error(`❌ Error cleaning ${filePath}:`, error.message);
        return false;
    }
}

function cleanAllViews() {
    console.log('🚀 Starting column search removal...\n');
    
    let cleanedCount = 0;
    let totalCount = viewsToClean.length;
    
    viewsToClean.forEach(viewPath => {
        if (removeColumnSearchFromView(viewPath)) {
            cleanedCount++;
        }
    });
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 CLEANUP RESULTS');
    console.log('='.repeat(50));
    console.log(`✅ Cleaned: ${cleanedCount}/${totalCount} files`);
    console.log(`📈 Success Rate: ${Math.round((cleanedCount / totalCount) * 100)}%`);
    
    if (cleanedCount === totalCount) {
        console.log('\n🎉 All views cleaned successfully!');
        console.log('\n📝 What remains:');
        console.log('✅ Default DESC order');
        console.log('✅ Global search (search box)');
        console.log('✅ Column sorting (click headers)');
        console.log('❌ Column search (removed)');
    } else {
        console.log('\n⚠️  Some files may need manual cleanup.');
    }
}

// Chạy script
if (require.main === module) {
    cleanAllViews();
}

module.exports = { removeColumnSearchFromView, cleanAllViews };
