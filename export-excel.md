# Cấu trúc Form Điều tra Y khoa - Định dạng Excel

## ✅ TRẠNG THÁI CẬP NHẬT
**Đ<PERSON> hoàn thành**: <PERSON><PERSON> sung đầy đủ chức năng exportToExcel cho tất cả các controller theo c<PERSON>u trúc database `patients(1).sql`

### Các Controller đã được cập nhật:
1. **hepstitisMt1Controller.js** ✅
   - Thêm các bảng: `viem_gan_mt1_kpa_not`, `times`
   - X<PERSON> lý multiple dates cho KPA và SGA
   - Làm phẳng dữ liệu theo key:value và key_n cho multiple dates

2. **hepatitisController.js** ✅
   - Thêm các bảng: `viem_gan_ctdd`, `viem_gan_td_ngt`, `viem_gan_td_not`, `viem_gan_tqau`, `viem_gan_ttdd`
   - <PERSON><PERSON> lý multiple dates cho tất cả các bảng
   - <PERSON><PERSON> loại dữ liệu theo ngày với suffix `_1`, `_2`, `_3`...

3. **tetanusController.js** ✅
   - Thêm các bảng: `uon_van_kpa`, `uon_van_med`
   - Xử lý multiple dates cho KPA và MED
   - Làm phẳng dữ liệu với prefix theo tên bảng

4. **liverSurgeryController.js** ✅
   - Thêm các bảng: `cat_gan_nho_kpa`, `phieu_hoi_chan_danh_gia`, `phieu_hoi_chan_ttc`
   - Xử lý multiple dates cho tất cả các phiếu
   - Phân loại theo ngày với suffix tương ứng

5. **standardController.js** ✅
   - Thêm các bảng: `research`, `patients_research`
   - Xử lý multiple research cho mỗi bệnh nhân
   - Tạo mapping với suffix theo thứ tự

6. **patientController.js** ✅
   - Cập nhật headers để bao gồm các phần F (KPA) và G (Times)
   - Cập nhật data mapping để xử lý các trường mới
   - Mở rộng createExcelFile để hỗ trợ nhiều loại dữ liệu hơn

### Cấu trúc Headers mới:
```
// PHẦN F - KHẨU PHẦN ĂN NỘI TRÚ (KPA)
'F1', 'F2', 'F3', 'F4', 'F5', 'F6', // KPA cơ bản
'F11', 'F12', 'F13', 'F14', 'F15', 'F16', // KPA ngày 1
'F21', 'F22', 'F23', 'F24', 'F25', 'F26', // KPA ngày 2
...

// PHẦN G - THỜI GIAN ĐIỀU TRỊ (TIMES)
'G1', 'G2', 'G3', 'G4', 'G5' // Times SGA
```

## Quy tắc định dạng
- **Tên biến**: Mã định danh
- **Nhãn biến**: Tên hiển thị  
- **Kí tự**: `_` = dạng chữ, `#` = dạng số
- **Ngày tháng**: `**/**/****` (DD/MM/YYYY)

---

## PHẦN A - THÔNG TIN CHUNG

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| A1 | hoten | Họ tên | ______________ | Ký tự dạng chữ |
| A2 | mabenhan | Mã bệnh án | __________ | Ký tự dạng chữ |
| A3 | ngaydieutra | Ngày điều tra | **/**/**** | DD/MM/YYYY |
| A4 | sophong | Phòng điều trị | ### | 3 ký tự số |
| A5 | dienthoai | Số điện thoại | ########## | 10 ký tự số |
| A6 | ngaysinh | Ngày sinh | **/**/**** | DD/MM/YYYY |
| A7 | sex | Giới tính | # | 1=Nam, 0=Nữ |
| A8 | dantoc | Dân tộc | # | 1=Kinh, 0=Khác _________ |
| A9 | hocvan | Trình độ học vấn | # | 1=Không đi học, 2=Tiểu học, 3=THCS, 4=THPT, 5=Đại học |
| A10 | nghenghiep | Nghề nghiệp | # | 1=Viên chức, 2=Nông dân, 3=Hưu trí, 4=Nội trợ, 5=Khác |
| A11 | noio | Nơi ở hiện tại | # | 1=Xã, 2=Phường |
| A12 | nguoidieutra | Điều tra viên | ________ | Ký tự dạng chữ |

---

## PHẦN B - THÔNG TIN BỆNH LÝ VÀ DINH DƯỠNG

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| B1 | chandoan | Chẩn đoán bệnh | # | 1=Viêm gan cấp, 2=Viêm gan mạn |
| B2 | nguyennhan | Nguyên nhân viêm gan | # | 1=Rượu, 2=Virus, 3=Độc chất, 4=Bệnh lý tự miễn, 5=Khác |
| B3 | cannang | Cân nặng | ## | 2 ký tự số (kg) |
| B4 | chieucao | Chiều cao | ##.# | 3 ký tự số (m) |
| B5 | bapchan | Chu vi vòng bắp chân | ## | 2 ký tự số (cm) |
| B6 | GOT | GOT | #### | 4 ký tự số |
| B7 | GPT | GPT | #### | 4 ký tự số |
| B8 | Hemo | Hemoglobin | ### | 3 ký tự số |
| B9 | buaphu | Ăn bữa phụ | # | 1=Có, 0=Không |
| B10 | buaphugi | Bữa phụ ăn gì | # | 1=Sữa, 2=Bánh kẹo, 3=Nước ngọt, 4=Quả chín, 5=Khác |
| B11 | ankieng | Ăn kiêng | # | 1=Có, 0=Không |
| B12 | kiengi | Ăn kiêng gì | # | 1=Rượu, 2=Bia, 3=Mỡ và thịt mỡ, 4=Chất đạm, 5=Khác |
| B13 | ruoubia | Uống rượu bia | # | 1=Có, 0=Không |
| B14 | tansuat | Tần suất sử dụng rượu bia | # | 1=Hàng ngày, 2=1-2 lần/tuần, 3=3-5 lần/tuần, 4=1-2 lần/tháng, 5=1-2 lần/năm |
| B15 | Luongbia | Uống bao nhiêu ml/ngày | #### | 4 ký tự số |
| B16 | nuocngot | Tuần qua sử dụng cafe, nước chè, nước có ga | # | 1=Có, 0=Không |
| B17 | nuocuongkhac | Tần suất sử dụng đồ uống khác | # | 0=Hàng ngày, 1=1-2 lần/tuần, 2=3-5 lần/tuần, 3=1-2 lần/tháng, 4=1-2 lần/năm |
| B18 | douongkhac | Sử dụng đồ uống gì | ___________________________ | Ký tự dạng chữ |
| B19 | lacay | Sử dụng lá cây | # | 1=Có, 0=Không |
| B20 | lacaygi | Lá cây sử dụng | ___________________________ | Ký tự dạng chữ |
| B21 | ghichu | Ghi chú | ________________________________ | Ký tự dạng chữ |

---

## PHẦN C - ĐÁNH GIÁ TÌNH TRẠNG DINH DƯỠNG

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| C1 | cannang6thang | Thay đổi cân nặng trong 6 tháng qua | # | 1=<5% giảm cân, 2=5-10%, 3=≥10% |
| C2 | tieuhoa | Triệu chứng tiêu hóa | # | 1=Không có triệu chứng, 2=Một chút nhưng không nặng, 3=Nhiều hoặc nặng |
| C3 | Phu | Phù (mắt cá chân hoặc vùng xương cùng) | # | 1=Không, 2=Nhẹ đến vừa, 3=Nặng |
| C4 | Cochuong | Cổ chướng (khám hoặc hỏi tiền sử) | # | 1=Không, 2=Nhẹ đến vừa, 3=Nặng |
| C5 | SGA | Phân loại | # | 1=Không có nguy cơ, 2=Nguy cơ mức độ nhẹ/vừa, 3=Nguy cơ cao |

---

## PHẦN D - THÔNG TIN BỆNH LÝ GAN VÀ DINH DƯỠNG

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| D1 | tinhtranggan | Tình trạng gan | # | 1=Xơ gan còn bù, 2=Xơ gan mất bù |
| D2 | mucdoxo | Mức độ xơ gan | # | 1=Child A, 2=Child B, 3=Child C |
| D3 | Albumin | Albumin | ### | 3 ký tự số |
| D4 | tuvan | Tư vấn về dinh dưỡng | # | 1=Có, 0=Không |
| D5 | sobuaan | Ăn bao nhiêu bữa/ngày | # | 1=<2 bữa, 2=3 bữa, 3=4 bữa, 4=5 bữa, 5=6 bữa |
| D6 | buadem | Bữa đêm | # | 1=Có, 0=Không |
| D7 | benhkem | Bệnh lý kèm theo | # | 1=ĐTĐ, 2=Suy thận, 3=Suy tim, 4=HIV, 5=Ung thư, 6=Nhiễm khuẩn, 7=Khác |

---

## PHẦN E - CHỈ SỐ DINH DƯỠNG

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| E1 | nangluong | Năng lượng | ####,# | 5 ký tự (4 số + 1 thập phân) |
| E2 | pro | Protein | ####,# | 5 ký tự (4 số + 1 thập phân) |
| E3 | lipid | Lipid | ####,# | 5 ký tự (4 số + 1 thập phân) |
| E4 | Glucid | Glucid | ####,# | 5 ký tự (4 số + 1 thập phân) |

---

## PHẦN F - KHẨU PHẦN ĂN NỘI TRÚ (KPA) 🆕

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| F1 | nd_duong_th_sang | Nội dung đường thẩm sáng | ______________ | Ký tự dạng chữ |
| F2 | nd_duong_th_trua | Nội dung đường thẩm trưa | ______________ | Ký tự dạng chữ |
| F3 | nd_duong_th_toi | Nội dung đường thẩm tối | ______________ | Ký tự dạng chữ |
| F4 | nd_duong_th_bua_phu | Nội dung đường thẩm bữa phụ | ______________ | Ký tự dạng chữ |
| F5 | nd_tinh_mac | Nội dung tĩnh mạch | ______________ | Ký tự dạng chữ |
| F6 | note_kpa | Ghi chú KPA | ______________ | Ký tự dạng chữ |

### Multiple dates cho KPA (F11-F56):
- **F11-F16**: KPA ngày 1
- **F21-F26**: KPA ngày 2  
- **F31-F36**: KPA ngày 3
- **F41-F46**: KPA ngày 4
- **F51-F56**: KPA ngày 5

---

## PHẦN G - THỜI GIAN ĐIỀU TRỊ (TIMES) 🆕

| Mã | Tên biến | Nhãn biến | Định dạng | Ghi chú |
|---|---|---|---|---|
| G1 | time_sga_1 | Thời gian SGA lần 1 | **/**/**** | DD/MM/YYYY |
| G2 | time_sga_2 | Thời gian SGA lần 2 | **/**/**** | DD/MM/YYYY |
| G3 | time_sga_3 | Thời gian SGA lần 3 | **/**/**** | DD/MM/YYYY |
| G4 | time_sga_4 | Thời gian SGA lần 4 | **/**/**** | DD/MM/YYYY |
| G5 | time_sga_5 | Thời gian SGA lần 5 | **/**/**** | DD/MM/YYYY |

---

## Hướng dẫn sử dụng

1. **Tạo file Excel** với các cột theo thứ tự: Mã biến, Tên biến, Nhãn biến, Định dạng dữ liệu
2. **Chú ý** đối với các trường hợp cho phép chọn nhiều giá trị thì sẽ tạo thêm cột ví dụ | B10 | buaphugi | Bữa phụ ăn gì | # | 1=Sữa, 2=Bánh kẹo, 3=Nước ngọt, 4=Quả chín, 5=Khác | sẽ thành B101 B102 ... và giá trị bên trong sẽ là 1 có 2 không. với trường hợp là dữ liệu theo ngày thì cũng làm tương tự. ví dụ ngày hôm nay cột B10 sẽ thành B1011 là lần thứ 1 ở giá trị thứ nhất số 1 ở sau cùng sẽ là số lần

3. **Lấy dữ liệu từ database** với các trường hợp 1 giá trị thì lấy theo key: value với trường hợp chọn nhiều giá trị thì lấy theo key1: nếu được chọn 1 nếu không được chọn là 0 , key2: 1, key3: 0 .... với trường hợp lấy tập hợp dữ liệu theo ngày cần lấy dữ liệu ngày và các dữ liệu theo ngày cũng thêm số vào cuối cùng ví dụ trường hợp ngày 1 thì là keyn1: value nếu là 1 giá trị. nếu nhiều giá trị thì key1n1: 1 nếu được chọn 0 nếu không được chọn. 

## 🎯 Kết quả đạt được

✅ **Hoàn thành 100%** chức năng exportToExcel cho tất cả controller
✅ **Bổ sung đầy đủ** các bảng từ database `patients(1).sql`  
✅ **Xử lý multiple dates** và multiple choice fields
✅ **Làm phẳng dữ liệu** theo quy tắc key:value với suffix _n cho dates
✅ **Tối ưu hiệu suất** với việc lấy dữ liệu song song
✅ **Chuẩn hóa format** Excel theo yêu cầu export-excel.md

**Các bảng đã được thêm vào**:
- `viem_gan_mt1_kpa_not`, `times` (MT1)
- `viem_gan_ctdd`, `viem_gan_td_ngt`, `viem_gan_td_not`, `viem_gan_tqau`, `viem_gan_ttdd` (Viêm gan)
- `uon_van_kpa`, `uon_van_med` (Uốn ván)
- `cat_gan_nho_kpa`, `phieu_hoi_chan_danh_gia`, `phieu_hoi_chan_ttc` (Cắt gan)
- `research`, `patients_research` (Standard) 

