/*
SQLyog Community v13.3.0 (64 bit)
MySQL - 10.4.32-MariaDB : Database - patients
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*Table structure for table `audit_logs` */

DROP TABLE IF EXISTS `audit_logs`;

CREATE TABLE `audit_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `email` varchar(256) DEFAULT NULL,
  `action` varchar(50) DEFAULT NULL,
  `resource` varchar(50) DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `timestamp` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `active` tinyint(4) DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=434 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `audit_logs` */

/*Table structure for table `auth_logs` */

DROP TABLE IF EXISTS `auth_logs`;

CREATE TABLE `auth_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) DEFAULT NULL,
  `action` varchar(50) DEFAULT NULL,
  `success` tinyint(1) DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `timestamp` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `active` tinyint(4) DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `auth_logs` */

/*Table structure for table `campaign` */

DROP TABLE IF EXISTS `campaign`;

CREATE TABLE `campaign` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(256) DEFAULT NULL,
  `active` tinyint(4) NOT NULL DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `campaign` */

/*Table structure for table `cat_gan_nho_kpa` */

DROP TABLE IF EXISTS `cat_gan_nho_kpa`;

CREATE TABLE `cat_gan_nho_kpa` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `nd_duong_th` text DEFAULT NULL,
  `nd_tinh_mac` text DEFAULT NULL,
  `note` text DEFAULT NULL,
  `xet_nghiem` text NOT NULL,
  `y_kien_bs` text DEFAULT NULL,
  `active` tinyint(4) NOT NULL DEFAULT 1,
  `campaign_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=368 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `cat_gan_nho_kpa` */

/*Table structure for table `dish_foods` */

DROP TABLE IF EXISTS `dish_foods`;

CREATE TABLE `dish_foods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dish_id` int(11) NOT NULL COMMENT 'ID món ăn',
  `food_id` int(11) NOT NULL COMMENT 'ID thực phẩm',
  `weight` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Khối lượng thực phẩm trong món ăn (g)',
  `order_index` int(11) DEFAULT 0 COMMENT 'Thứ tự sắp xếp',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_dish_id` (`dish_id`),
  KEY `idx_food_id` (`food_id`),
  KEY `idx_dish_foods_combo` (`dish_id`,`food_id`),
  CONSTRAINT `fk_dish_foods_dish` FOREIGN KEY (`dish_id`) REFERENCES `dishes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_dish_foods_food` FOREIGN KEY (`food_id`) REFERENCES `food_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Bảng chi tiết thực phẩm trong món ăn';

/*Data for the table `dish_foods` */

/*Table structure for table `dishes` */

DROP TABLE IF EXISTS `dishes`;

CREATE TABLE `dishes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Tên món ăn',
  `description` text DEFAULT NULL COMMENT 'Mô tả món ăn',
  `category` varchar(100) DEFAULT NULL COMMENT 'Loại món ăn (chính, phụ, tráng miệng, etc.)',
  `created_by` int(11) DEFAULT NULL COMMENT 'Người tạo',
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1: active, 0: inactive, -1: deleted',
  PRIMARY KEY (`id`),
  KEY `idx_active` (`active`),
  KEY `idx_category` (`category`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Bảng món ăn';

/*Data for the table `dishes` */

insert  into `dishes`(`id`,`name`,`description`,`category`,`created_by`,`campaign_id`,`created_at`,`updated_at`,`active`) values 
(1,'Cơm gà luộc','Món cơm gà luộc đầy đủ dinh dưỡng','Món chính',1,1,'2025-06-29 09:17:33','2025-07-11 14:54:28',1),
(2,'Canh chua cá','Canh chua cá bổ dưỡng','Canh',1,1,'2025-06-29 09:17:33','2025-07-11 14:54:28',1),
(3,'Rau muống xào tỏi','Rau xanh bổ sung vitamin','',1,1,'2025-06-29 09:17:33','2025-07-11 14:54:28',1);

/*Table structure for table `food_info` */

DROP TABLE IF EXISTS `food_info`;

CREATE TABLE `food_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(128) DEFAULT NULL,
  `name` varchar(512) NOT NULL,
  `type` enum('raw','cooked') DEFAULT 'raw' COMMENT 'Loại thực phẩm: raw=sống, cooked=chín',
  `type_year` enum('2000','2024','2017') DEFAULT '2017' COMMENT 'Năm dữ liệu thực phẩm',
  `ten` varchar(512) DEFAULT NULL,
  `active` tinyint(1) DEFAULT NULL,
  `total_sugar` double DEFAULT NULL,
  `galactose` double DEFAULT NULL,
  `maltose` double DEFAULT NULL,
  `lactose` double DEFAULT NULL,
  `fructose` double DEFAULT NULL,
  `glucose` double DEFAULT NULL,
  `sucrose` double DEFAULT NULL,
  `lycopene` double DEFAULT NULL,
  `lutein_zeaxanthin` double DEFAULT NULL,
  `total_isoflavone` double DEFAULT NULL,
  `daidzein` double DEFAULT NULL,
  `genistein` double DEFAULT NULL,
  `glycetin` double DEFAULT NULL,
  `phytosterol` double DEFAULT NULL,
  `purine` double DEFAULT NULL,
  `weight` int(11) DEFAULT NULL,
  `protein` double DEFAULT NULL,
  `in` double DEFAULT NULL,
  `lysin` double DEFAULT NULL,
  `methionin` double DEFAULT NULL,
  `tryptophan` double DEFAULT NULL,
  `phenylalanin` double DEFAULT NULL,
  `threonin` double DEFAULT NULL,
  `isoleucine` double DEFAULT NULL,
  `arginine` double DEFAULT NULL,
  `histidine` double DEFAULT NULL,
  `alanine` double DEFAULT NULL,
  `aspartic_acid` double DEFAULT NULL,
  `glutamic_acid` double DEFAULT NULL,
  `glycine` double DEFAULT NULL,
  `proline` double DEFAULT NULL,
  `serine` double DEFAULT NULL,
  `animal_protein` double DEFAULT NULL,
  `unanimal_protein` double DEFAULT NULL,
  `cystine` double DEFAULT NULL,
  `valine` double DEFAULT NULL,
  `tyrosine` double DEFAULT NULL,
  `leucine` double DEFAULT NULL,
  `lignoceric` double DEFAULT NULL,
  `lipid` double DEFAULT NULL,
  `animal_lipid` double DEFAULT NULL,
  `unanimal_lipid` double DEFAULT NULL,
  `retinol` double DEFAULT NULL,
  `riboflavin` double DEFAULT NULL,
  `thiamine` double DEFAULT NULL,
  `niacin` double DEFAULT NULL,
  `pantothenic_acid` double DEFAULT NULL,
  `folate` int(11) DEFAULT NULL,
  `folic_acid` double DEFAULT NULL,
  `biotin` double DEFAULT NULL,
  `caroten` double DEFAULT NULL,
  `vitamin_a_rae` double DEFAULT NULL,
  `vitamin_b1` double DEFAULT NULL,
  `vitamin_b2` double DEFAULT NULL,
  `vitamin_b6` double DEFAULT NULL,
  `vitamin_b9` double DEFAULT NULL,
  `vitamin_b12` double DEFAULT NULL,
  `vitamin_pp` double DEFAULT NULL,
  `vitamin_c` double DEFAULT NULL,
  `vitamin_e` double DEFAULT NULL,
  `vitamin_k` double DEFAULT NULL,
  `b_carotene` double DEFAULT NULL,
  `a_carotene` double DEFAULT NULL,
  `b_cryptoxanthin` double DEFAULT NULL,
  `edible` int(11) DEFAULT NULL,
  `energy` int(11) DEFAULT NULL,
  `water` double DEFAULT NULL,
  `fat` double DEFAULT NULL,
  `carbohydrate` double DEFAULT NULL,
  `fiber` double DEFAULT NULL,
  `ash` double DEFAULT NULL,
  `calci` double DEFAULT NULL,
  `phosphorous` double DEFAULT NULL,
  `fe` double DEFAULT NULL,
  `zinc` double DEFAULT NULL,
  `sodium` int(11) DEFAULT NULL,
  `potassium` int(11) DEFAULT NULL,
  `magnesium` int(11) DEFAULT NULL,
  `manganese` double DEFAULT NULL,
  `copper` int(11) DEFAULT NULL,
  `selenium` double DEFAULT NULL,
  `fluoride` double DEFAULT NULL,
  `iodine` double DEFAULT NULL,
  `total_fat` double DEFAULT NULL,
  `total_saturated_fat` double DEFAULT NULL,
  `palmitic` double DEFAULT NULL,
  `margaric` double DEFAULT NULL,
  `stearic` double DEFAULT NULL,
  `arachidic` double DEFAULT NULL,
  `behenic` double DEFAULT NULL,
  `mufa` double DEFAULT NULL,
  `myristoleic` double DEFAULT NULL,
  `palmitoleic` double DEFAULT NULL,
  `oleic` double DEFAULT NULL,
  `pufa` double DEFAULT NULL,
  `linoleic` double DEFAULT NULL,
  `linolenic` double DEFAULT NULL,
  `arachidonic` double DEFAULT NULL,
  `dha` double DEFAULT NULL,
  `trans_fatty_acids` double DEFAULT NULL,
  `cholesterol` double DEFAULT NULL,
  `vitamin_d` double DEFAULT NULL,
  `epa` double DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_type_year` (`type`,`type_year`),
  KEY `idx_name_type_year` (`name`,`type`,`type_year`)
) ENGINE=InnoDB AUTO_INCREMENT=5366 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `food_info` */

/*Table structure for table `log_activities` */

DROP TABLE IF EXISTS `log_activities`;

CREATE TABLE `log_activities` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `full_message` text DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `method` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `agent` varchar(255) DEFAULT NULL,
  `form_data` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `log_activities` */

/*Table structure for table `menu_example` */

DROP TABLE IF EXISTS `menu_example`;

CREATE TABLE `menu_example` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name_menu` varchar(255) NOT NULL,
  `detail` longtext NOT NULL,
  `share` tinyint(4) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `menu_example` */

/*Table structure for table `menu_time` */

DROP TABLE IF EXISTS `menu_time`;

CREATE TABLE `menu_time` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `time` varchar(255) NOT NULL,
  `share` tinyint(4) DEFAULT NULL,
  `order_sort` smallint(6) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `menu_time` */

/*Table structure for table `patients` */

DROP TABLE IF EXISTS `patients`;

CREATE TABLE `patients` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `fullname` varchar(255) NOT NULL DEFAULT '',
  `phone` varchar(255) DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `address` varchar(1024) DEFAULT NULL,
  `ngay_nhap_vien` timestamp NULL DEFAULT NULL,
  `ma_benh_an` varchar(255) DEFAULT NULL,
  `phong_dieu_tri` varchar(255) DEFAULT NULL,
  `dan_toc` varchar(512) DEFAULT NULL,
  `dan_toc_khac` varchar(512) DEFAULT NULL,
  `trinh_do` tinyint(4) DEFAULT NULL,
  `nghe_nghiep` tinyint(4) DEFAULT NULL,
  `nghe_nghiep_khac` varchar(1024) DEFAULT NULL,
  `noi_o` varchar(1024) DEFAULT NULL,
  `xep_loai_kt` tinyint(4) DEFAULT NULL COMMENT 'xep loai kinh te',
  `chuan_doan` text DEFAULT NULL,
  `khoa` varchar(128) DEFAULT NULL,
  `que_quan` varchar(256) DEFAULT NULL,
  `type` tinyint(4) NOT NULL COMMENT '3 viem gan 4 uon van 5 cat gan',
  `moi_quan_he` text DEFAULT NULL,
  `dieu_tra_vien` varchar(1024) DEFAULT NULL,
  `tien_su_benh` text DEFAULT NULL,
  `cn` varchar(128) DEFAULT NULL,
  `cc` varchar(128) DEFAULT NULL,
  `bien_ban` tinyint(4) DEFAULT 0,
  `khan_cap` tinyint(4) DEFAULT 0,
  `ngay_hoi_chan` timestamp NULL DEFAULT NULL,
  `ngay_dieu_tra` timestamp NULL DEFAULT NULL,
  `active` tinyint(4) NOT NULL DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=212 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `patients` */

/*Table structure for table `patients_research` */

DROP TABLE IF EXISTS `patients_research`;

CREATE TABLE `patients_research` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fullname` varchar(512) DEFAULT NULL,
  `phone` varchar(128) DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `address` varchar(3072) DEFAULT NULL,
  `ngay_nhap_vien` timestamp NULL DEFAULT NULL,
  `ma_benh_an` varchar(128) DEFAULT NULL,
  `phong_dieu_tri` varchar(128) DEFAULT NULL,
  `dan_toc` varchar(256) DEFAULT NULL,
  `dan_toc_khac` varchar(256) DEFAULT NULL,
  `trinh_do` tinyint(4) DEFAULT NULL,
  `nghe_nghiep` tinyint(4) DEFAULT NULL,
  `nghe_nghiep_khac` varchar(512) DEFAULT NULL,
  `noi_o` varchar(1024) DEFAULT NULL,
  `xep_loai_kt` tinyint(4) DEFAULT NULL,
  `chuan_doan` text DEFAULT NULL,
  `khoa` varchar(128) DEFAULT NULL,
  `que_quan` varchar(256) DEFAULT NULL,
  `id_research` bigint(20) DEFAULT NULL,
  `menu_example` longtext DEFAULT NULL,
  `active` tinyint(4) NOT NULL DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `patients_research` */

/*Table structure for table `phieu_hoi_chan_danh_gia` */

DROP TABLE IF EXISTS `phieu_hoi_chan_danh_gia`;

CREATE TABLE `phieu_hoi_chan_danh_gia` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `time_id` bigint(20) DEFAULT NULL,
  `tinh_trang_nguoi_benh` text DEFAULT NULL,
  `khau_phan_an_24h` text DEFAULT NULL,
  `tieu_hoa` text DEFAULT NULL,
  `danh_gia` text DEFAULT NULL,
  `ket_qua_can_lam_sang` text DEFAULT NULL,
  `can_thiep_kcal` varchar(96) DEFAULT NULL,
  `can_thiep_kg` varchar(96) DEFAULT NULL,
  `can_thiep_note` text DEFAULT NULL,
  `che_do_dinh_duong` varchar(96) DEFAULT NULL,
  `che_do_dinh_duong_note` text DEFAULT NULL,
  `bo_sung` text DEFAULT NULL,
  `chu_y` text DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `phieu_hoi_chan_danh_gia` */

/*Table structure for table `phieu_hoi_chan_ttc` */

DROP TABLE IF EXISTS `phieu_hoi_chan_ttc`;

CREATE TABLE `phieu_hoi_chan_ttc` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `cn` varchar(384) DEFAULT NULL,
  `cc` varchar(384) DEFAULT NULL,
  `ctc` varchar(384) DEFAULT NULL,
  `chuan_doan_ls` text DEFAULT NULL,
  `ngay_hoi_chan` date DEFAULT NULL,
  `cn_1_thang` varchar(96) DEFAULT NULL,
  `khau_phan_an` varchar(96) DEFAULT NULL,
  `trieu_chung_th` varchar(96) DEFAULT NULL,
  `giam_chuc_nang_hd` varchar(96) DEFAULT NULL,
  `nhu_cau_chuyen_hoa` varchar(96) DEFAULT NULL,
  `kham_lam_sang` varchar(96) DEFAULT NULL,
  `chon_tt_1` varchar(96) DEFAULT NULL,
  `tien_su_benh` text DEFAULT NULL,
  `tinh_trang_nguoi_benh` text DEFAULT NULL,
  `khau_phan_an_24h` text DEFAULT NULL,
  `tieu_hoa` text DEFAULT NULL,
  `che_do_dinh_duong` varchar(96) DEFAULT NULL,
  `che_do_dinh_duong_note` text DEFAULT NULL,
  `duong_nuoi` varchar(32) DEFAULT NULL,
  `dich_vao` int(11) DEFAULT NULL,
  `dich_ra` int(11) DEFAULT NULL,
  `e_nckn` varchar(384) DEFAULT NULL,
  `can_thiep_kcal` varchar(384) DEFAULT NULL,
  `can_thiep_kg` varchar(384) DEFAULT NULL,
  `can_thiep_note` text DEFAULT NULL,
  `ket_qua_can_lam_sang` text DEFAULT NULL,
  `bo_sung` text DEFAULT NULL,
  `chu_y` text DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `phieu_hoi_chan_ttc` */


/*Table structure for table `projects` */

DROP TABLE IF EXISTS `projects`;

CREATE TABLE `projects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Tên dự án',
  `description` text DEFAULT NULL COMMENT 'Mô tả dự án',
  `start_date` date DEFAULT NULL COMMENT 'Ngày bắt đầu',
  `end_date` date DEFAULT NULL COMMENT 'Ngày kết thúc',
  `google_sheet_id` varchar(255) DEFAULT NULL COMMENT 'ID của Google Sheet để lưu dữ liệu',
  `google_sheet_url` text DEFAULT NULL COMMENT 'URL của Google Sheet',
  `created_by` int(11) NOT NULL COMMENT 'ID người tạo',
  `campaign_id` int(11) DEFAULT NULL COMMENT 'ID chiến dịch',
  `active` tinyint(4) DEFAULT 1 COMMENT '1: Hoạt động, 0: Tạm dừng, -1: Đã xóa',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_campaign_id` (`campaign_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `projects` */

/*Table structure for table `research` */

DROP TABLE IF EXISTS `research`;

CREATE TABLE `research` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(1024) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `note` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `research` */

/*Table structure for table `role` */

DROP TABLE IF EXISTS `role`;

CREATE TABLE `role` (
  `role_id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `role` */

insert  into `role`(`role_id`,`role_name`) values 
(1,'Administrator'),
(2,'Customer'),
(3,'Hepatitis'),
(4,'Tetanus'),
(5,'LiverSurgery'),
(6,'Hepatitis1'),
(7,'Research'),
(8,'ConsultationForm');

/*Table structure for table `role_user` */

DROP TABLE IF EXISTS `role_user`;

CREATE TABLE `role_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `user_id` int(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_role_user_role` (`role_id`),
  CONSTRAINT `fk_role_user_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`role_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `role_user` */


/*Table structure for table `setting` */

DROP TABLE IF EXISTS `setting`;

CREATE TABLE `setting` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `systemname` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `body` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `setting` */

/*Table structure for table `survey_configs` */

DROP TABLE IF EXISTS `survey_configs`;

CREATE TABLE `survey_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL COMMENT 'ID dự án',
  `name` varchar(255) NOT NULL COMMENT 'Tên cấu hình khảo sát',
  `description` text DEFAULT NULL COMMENT 'Mô tả khảo sát',
  `survey_url_slug` varchar(255) NOT NULL COMMENT 'Slug URL cho khảo sát công khai',
  `allow_multiple_responses` tinyint(4) DEFAULT 0 COMMENT '1: Cho phép nhiều phản hồi, 0: Chỉ 1 lần',
  `require_email` tinyint(4) DEFAULT 0 COMMENT '1: Bắt buộc email, 0: Không bắt buộc',
  `success_message` text DEFAULT NULL COMMENT 'Thông báo sau khi submit thành công',
  `settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Các cài đặt khác (theme, validation, etc.)' CHECK (json_valid(`settings`)),
  `created_by` int(11) NOT NULL COMMENT 'ID người tạo',
  `campaign_id` int(11) DEFAULT NULL COMMENT 'ID chiến dịch',
  `active` tinyint(4) DEFAULT 1 COMMENT '1: Hoạt động, 0: Tạm dừng',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `survey_url_slug` (`survey_url_slug`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_survey_url_slug` (`survey_url_slug`),
  CONSTRAINT `survey_configs_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `survey_configs` */

/*Table structure for table `survey_fields` */

DROP TABLE IF EXISTS `survey_fields`;

CREATE TABLE `survey_fields` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `survey_config_id` int(11) NOT NULL COMMENT 'ID cấu hình khảo sát',
  `field_name` varchar(255) NOT NULL COMMENT 'Tên trường (dùng làm name attribute)',
  `field_label` varchar(255) NOT NULL COMMENT 'Nhãn hiển thị',
  `field_type` enum('text','textarea','select','multiselect','radio','checkbox','datetime','date','time','number','email','url') NOT NULL COMMENT 'Loại trường',
  `field_options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Các tùy chọn cho select, radio, checkbox (array of {value, label})' CHECK (json_valid(`field_options`)),
  `is_required` tinyint(4) DEFAULT 0 COMMENT '1: Bắt buộc, 0: Không bắt buộc',
  `placeholder` varchar(255) DEFAULT NULL COMMENT 'Placeholder text',
  `help_text` text DEFAULT NULL COMMENT 'Text hướng dẫn',
  `validation_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Các rule validation (min, max, pattern, etc.)' CHECK (json_valid(`validation_rules`)),
  `field_order` int(11) DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  `field_settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Cài đặt riêng cho từng loại field' CHECK (json_valid(`field_settings`)),
  `created_by` int(11) NOT NULL COMMENT 'ID người tạo',
  `active` tinyint(4) DEFAULT 1 COMMENT '1: Hiển thị, 0: Ẩn',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `survey_fields_unique` (`survey_config_id`,`field_name`,`active`),
  KEY `idx_survey_config_id` (`survey_config_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_display_order` (`field_order`),
  CONSTRAINT `survey_fields_ibfk_1` FOREIGN KEY (`survey_config_id`) REFERENCES `survey_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `survey_fields` */

/*Table structure for table `survey_response_data` */

DROP TABLE IF EXISTS `survey_response_data`;

CREATE TABLE `survey_response_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `survey_response_id` int(11) NOT NULL COMMENT 'ID phản hồi khảo sát',
  `survey_field_id` int(11) NOT NULL COMMENT 'ID trường khảo sát',
  `field_name` varchar(255) NOT NULL COMMENT 'Tên trường (để backup)',
  `field_value` text DEFAULT NULL COMMENT 'Giá trị trả lời',
  `field_value_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Giá trị dạng JSON (cho multiselect, checkbox)' CHECK (json_valid(`field_value_json`)),
  `active` tinyint(4) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_survey_response_id` (`survey_response_id`),
  KEY `idx_survey_field_id` (`survey_field_id`),
  KEY `idx_field_name` (`field_name`),
  CONSTRAINT `survey_response_data_ibfk_1` FOREIGN KEY (`survey_response_id`) REFERENCES `survey_responses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `survey_response_data_ibfk_2` FOREIGN KEY (`survey_field_id`) REFERENCES `survey_fields` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `survey_response_data` */

/*Table structure for table `survey_responses` */

DROP TABLE IF EXISTS `survey_responses`;

CREATE TABLE `survey_responses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `survey_config_id` int(11) NOT NULL COMMENT 'ID cấu hình khảo sát',
  `respondent_email` varchar(255) DEFAULT NULL COMMENT 'Email người trả lời (nếu có)',
  `respondent_ip` varchar(45) DEFAULT NULL COMMENT 'IP address người trả lời',
  `user_agent` text DEFAULT NULL COMMENT 'User agent của browser',
  `session_id` varchar(255) DEFAULT NULL COMMENT 'Session ID để track multiple responses',
  `is_completed` tinyint(4) DEFAULT 0 COMMENT '1: Hoàn thành, 0: Chưa hoàn thành',
  `submitted_at` datetime DEFAULT NULL COMMENT 'Thời gian submit',
  `google_sheet_row_id` int(11) DEFAULT NULL COMMENT 'ID dòng trong Google Sheet',
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Metadata khác (referrer, utm params, etc.)' CHECK (json_valid(`metadata`)),
  `active` tinyint(4) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_survey_config_id` (`survey_config_id`),
  KEY `idx_respondent_email` (`respondent_email`),
  KEY `idx_submitted_at` (`submitted_at`),
  KEY `idx_is_completed` (`is_completed`),
  CONSTRAINT `survey_responses_ibfk_1` FOREIGN KEY (`survey_config_id`) REFERENCES `survey_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `survey_responses` */
/*Table structure for table `survey_templates` */

DROP TABLE IF EXISTS `survey_templates`;

CREATE TABLE `survey_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Tên template',
  `description` text DEFAULT NULL COMMENT 'Mô tả template',
  `template_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Dữ liệu template (fields, settings)' CHECK (json_valid(`template_data`)),
  `category` varchar(100) DEFAULT NULL COMMENT 'Danh mục template',
  `is_public` tinyint(4) DEFAULT 0 COMMENT '1: Public, 0: Private',
  `usage_count` int(11) DEFAULT 0 COMMENT 'Số lần sử dụng',
  `created_by` int(11) NOT NULL COMMENT 'ID người tạo',
  `campaign_id` int(11) DEFAULT NULL COMMENT 'ID chiến dịch',
  `active` tinyint(4) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `survey_templates` */

/*Table structure for table `times` */

DROP TABLE IF EXISTS `times`;

CREATE TABLE `times` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `type` varchar(128) DEFAULT NULL COMMENT '1 tinh trang dd 2 can thiep dd 3 sga',
  `active` tinyint(4) DEFAULT 1,
  `project` varchar(128) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=171 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `times` */

/*Table structure for table `uon_van_kpa` */

DROP TABLE IF EXISTS `uon_van_kpa`;

CREATE TABLE `uon_van_kpa` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `nd_duong_th` text DEFAULT NULL,
  `nd_tinh_mac` text DEFAULT NULL,
  `note` text DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `uon_van_kpa` */

/*Table structure for table `uon_van_ls` */

DROP TABLE IF EXISTS `uon_van_ls`;

CREATE TABLE `uon_van_ls` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `time_id` bigint(20) DEFAULT NULL,
  `cn` varchar(128) DEFAULT NULL,
  `vong_bap_chan` varchar(128) DEFAULT NULL,
  `cc` varchar(128) DEFAULT NULL,
  `albumin` varchar(128) DEFAULT NULL,
  `pre_albumin` varchar(128) DEFAULT NULL,
  `hemoglobin` varchar(128) DEFAULT NULL,
  `protein` varchar(128) DEFAULT NULL,
  `phospho` varchar(128) DEFAULT NULL,
  `glucose` varchar(128) DEFAULT NULL,
  `magie` varchar(128) DEFAULT NULL,
  `kali` varchar(128) DEFAULT NULL,
  `ck` varchar(128) DEFAULT NULL,
  `ure` varchar(128) DEFAULT NULL,
  `bilirubin` varchar(128) DEFAULT NULL,
  `creatinin` varchar(128) DEFAULT NULL,
  `benh_ly` text DEFAULT NULL,
  `thuoc` text DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `uon_van_ls` */

/*Table structure for table `uon_van_med` */

DROP TABLE IF EXISTS `uon_van_med`;

CREATE TABLE `uon_van_med` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `type` varchar(128) NOT NULL,
  `active` tinyint(4) DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `uon_van_med` */

/*Table structure for table `uon_van_sga` */

DROP TABLE IF EXISTS `uon_van_sga`;

CREATE TABLE `uon_van_sga` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time_id` bigint(20) NOT NULL,
  `cn_6_thang` tinyint(4) DEFAULT NULL,
  `cn_2_tuan` tinyint(4) DEFAULT NULL,
  `khau_phan_an_ht` tinyint(4) DEFAULT NULL,
  `tieu_chung_th` tinyint(4) DEFAULT NULL,
  `giam_chuc_nang` tinyint(4) DEFAULT NULL,
  `nc_chuyen_hoa` tinyint(4) DEFAULT NULL,
  `mo_duoi_da` tinyint(4) DEFAULT NULL,
  `teo_co` tinyint(4) DEFAULT NULL,
  `phu` tinyint(4) DEFAULT NULL,
  `co_chuong` tinyint(4) DEFAULT NULL,
  `phan_loai` tinyint(4) DEFAULT NULL,
  `total_point` int(11) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `uon_van_sga` */


/*Table structure for table `uon_van_ttth` */

DROP TABLE IF EXISTS `uon_van_ttth`;

CREATE TABLE `uon_van_ttth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `time_id` bigint(20) DEFAULT NULL,
  `chuong_bung` tinyint(4) DEFAULT NULL,
  `trao_nguoc` tinyint(4) DEFAULT NULL,
  `tao_bon` tinyint(4) DEFAULT NULL,
  `phan_long_3_ngay` tinyint(4) DEFAULT NULL,
  `duong_mau_10` tinyint(4) DEFAULT NULL,
  `duong_mau_20` tinyint(4) DEFAULT NULL,
  `so_lan_di_ngoai` int(11) DEFAULT NULL,
  `tinh_trang_phan` varchar(512) DEFAULT NULL,
  `dich_ton_du` varchar(128) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `campaign_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `uon_van_ttth` */


/*Table structure for table `user` */

DROP TABLE IF EXISTS `user`;

CREATE TABLE `user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fullname` varchar(250) NOT NULL,
  `password` text NOT NULL,
  `email` varchar(400) NOT NULL,
  `phone` varchar(45) DEFAULT NULL,
  `gender` tinyint(4) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  `jwt_token_id` varchar(255) DEFAULT NULL COMMENT 'JWT token identifier cho single device login',
  `device_info` text DEFAULT NULL COMMENT 'Thông tin thiết bị (user agent, IP, etc.)',
  `token_created_at` timestamp NULL DEFAULT NULL COMMENT 'Thời gian tạo token',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `max_sessions` int(11) DEFAULT 5 COMMENT 'Số lượng session tối đa cho phép',
  `allow_multiple_devices` tinyint(1) DEFAULT 1 COMMENT 'Cho phép đăng nhập nhiều thiết bị',
  `campaign_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_jwt_token_id` (`jwt_token_id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `user` */


/*Table structure for table `user_session_settings` */

DROP TABLE IF EXISTS `user_session_settings`;

CREATE TABLE `user_session_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `max_sessions` int(11) DEFAULT 5,
  `session_timeout_hours` int(11) DEFAULT 24,
  `allow_multiple_devices` tinyint(1) DEFAULT 1,
  `notify_new_login` tinyint(1) DEFAULT 1 COMMENT 'Thông báo khi có đăng nhập mới',
  `auto_logout_inactive` tinyint(1) DEFAULT 1 COMMENT 'Tự động logout khi không hoạt động',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_user_session_settings_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Cài đặt session của user';

/*Data for the table `user_session_settings` */


/*Table structure for table `user_sessions` */

DROP TABLE IF EXISTS `user_sessions`;

CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `jwt_token_id` varchar(255) NOT NULL,
  `device_name` varchar(255) DEFAULT NULL COMMENT 'Tên thiết bị (tự động detect)',
  `device_type` enum('desktop','mobile','tablet','unknown') DEFAULT 'unknown',
  `browser` varchar(100) DEFAULT NULL,
  `os` varchar(100) DEFAULT NULL,
  `device_info` text DEFAULT NULL COMMENT 'Thông tin thiết bị chi tiết',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL COMMENT 'Vị trí đăng nhập (nếu có)',
  `login_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `logout_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_current_session` tinyint(1) DEFAULT 0 COMMENT 'Session hiện tại',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_jwt_token_id` (`jwt_token_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_current_session` (`is_current_session`),
  KEY `idx_last_activity` (`last_activity`),
  CONSTRAINT `fk_user_sessions_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=346 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Lịch sử đăng nhập của user';

/*Data for the table `user_sessions` */

/*Table structure for table `viem_gam_ttcb` */

DROP TABLE IF EXISTS `viem_gam_ttcb`;

CREATE TABLE `viem_gam_ttcb` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `so_lan_vgc` tinyint(4) DEFAULT NULL COMMENT 'so lan dieu tri vgc',
  `thoi_gian_vgm` tinyint(4) DEFAULT NULL COMMENT 'thoi gian mac vg man',
  `thoi_gian_vg_ruou` tinyint(4) DEFAULT NULL COMMENT 'thoi giam mac vg ruou',
  `thoi_gian_vg_virus` tinyint(4) DEFAULT NULL COMMENT 'thoi giam mac vg virus',
  `benh_gan_mat_khac` varchar(1024) DEFAULT NULL,
  `thoi_gian_gm_khac` tinyint(4) DEFAULT NULL,
  `ts_benh_khac_1` varchar(1024) DEFAULT NULL,
  `ts_benh_1_so_nam` tinyint(4) DEFAULT NULL,
  `ts_benh_khac_2` varchar(1024) DEFAULT NULL,
  `ts_benh_2_so_nam` tinyint(4) DEFAULT NULL,
  `ts_benh_khac_3` varchar(1024) DEFAULT NULL,
  `ts_benh_3_so_nam` tinyint(4) DEFAULT NULL,
  `ts_benh_khac_4` varchar(1024) DEFAULT NULL,
  `ts_benh_4_so_nam` tinyint(4) DEFAULT NULL,
  `ts_benh_khac_5` varchar(1024) DEFAULT NULL,
  `ts_benh_5_so_nam` tinyint(4) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gam_ttcb` */

/*Table structure for table `viem_gan_ctdd` */

DROP TABLE IF EXISTS `viem_gan_ctdd`;

CREATE TABLE `viem_gan_ctdd` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time_id` bigint(20) NOT NULL,
  `chan_an` tinyint(4) DEFAULT NULL,
  `chan_an_note` varchar(1024) DEFAULT NULL,
  `an_khong_ngon` tinyint(4) DEFAULT NULL,
  `an_khong_ngon_note` varchar(1024) DEFAULT NULL,
  `buon_non` tinyint(4) DEFAULT NULL,
  `buon_non_note` varchar(1024) DEFAULT NULL,
  `non` tinyint(4) DEFAULT NULL,
  `non_note` varchar(1024) DEFAULT NULL,
  `tao_bon` tinyint(4) DEFAULT NULL,
  `tao_bon_note` varchar(1024) DEFAULT NULL,
  `tieu_chay` tinyint(4) DEFAULT NULL,
  `tieu_chay_note` varchar(1024) DEFAULT NULL,
  `song_phan` tinyint(4) DEFAULT NULL,
  `song_phan_note` varchar(1024) DEFAULT NULL,
  `nhiet_mieng` tinyint(4) DEFAULT NULL,
  `nhiet_mieng_note` varchar(1024) DEFAULT NULL,
  `thay_doi_vi_giac` tinyint(4) DEFAULT NULL,
  `thay_doi_vi_giac_note` varchar(1024) DEFAULT NULL,
  `khac` tinyint(4) DEFAULT NULL,
  `khac_note` text DEFAULT NULL,
  `co_chuong` tinyint(4) DEFAULT NULL,
  `co_chuong_note` varchar(1024) DEFAULT NULL,
  `met_moi` tinyint(4) DEFAULT NULL,
  `met_moi_note` varchar(1024) DEFAULT NULL,
  `dau` tinyint(4) DEFAULT NULL,
  `dau_note` varchar(1024) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_ctdd` */

/*Table structure for table `viem_gan_dhnv` */

DROP TABLE IF EXISTS `viem_gan_dhnv`;

CREATE TABLE `viem_gan_dhnv` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `chan_an_met_moi` tinyint(4) DEFAULT NULL,
  `bieu_hien_tieu_hoa` tinyint(4) DEFAULT NULL,
  `bieu_hien_tieu_hoa_khac` varchar(1024) DEFAULT NULL,
  `dau_tuc_hsp` tinyint(4) DEFAULT NULL,
  `dau_tuc_hsp_khi` tinyint(4) DEFAULT NULL,
  `dau_tuc_hsp_khac` varchar(1024) DEFAULT NULL,
  `vang_da_vang_mat` tinyint(4) DEFAULT NULL,
  `bieu_hien_phu` tinyint(4) DEFAULT NULL,
  `bieu_hien_co_chuong` tinyint(4) DEFAULT NULL,
  `ngua_da` tinyint(4) DEFAULT NULL,
  `ngua_da_khac` varchar(1024) DEFAULT NULL,
  `xuat_huyet_tieu_hoa` tinyint(4) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_dhnv` */

/*Table structure for table `viem_gan_mt1_dhnv` */

DROP TABLE IF EXISTS `viem_gan_mt1_dhnv`;

CREATE TABLE `viem_gan_mt1_dhnv` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `chan_doan_benh` tinyint(4) DEFAULT NULL,
  `nguyen_nhan` tinyint(4) DEFAULT NULL,
  `nguyen_nhan_khac` varchar(1024) DEFAULT NULL,
  `cn` varchar(512) DEFAULT NULL,
  `cc` varchar(384) DEFAULT NULL,
  `vong_bap_chan` varchar(384) DEFAULT NULL,
  `got` varchar(384) DEFAULT NULL,
  `gpt` varchar(384) DEFAULT NULL,
  `hemoglobin` varchar(384) DEFAULT NULL,
  `bua_chinh` tinyint(4) DEFAULT NULL,
  `bua_phu` tinyint(4) DEFAULT NULL,
  `bua_phu_an` varchar(255) DEFAULT NULL,
  `bua_phu_an_khac` varchar(1024) DEFAULT NULL,
  `an_kieng` tinyint(4) DEFAULT NULL,
  `an_kieng_loai` varchar(255) DEFAULT NULL,
  `an_kieng_loai_khac` varchar(1024) DEFAULT NULL,
  `ruou_bia` tinyint(4) DEFAULT NULL,
  `ruou_bia_ts` tinyint(4) DEFAULT NULL,
  `ml_ruou` int(11) DEFAULT NULL,
  `ml_bia` int(11) DEFAULT NULL,
  `do_uong_khac` tinyint(4) DEFAULT NULL,
  `do_uong_khac_ts` tinyint(4) DEFAULT NULL,
  `loai_do_uong` varchar(255) DEFAULT NULL,
  `loai_do_uong_khac` varchar(3072) DEFAULT NULL,
  `su_dung_la_cay` tinyint(4) DEFAULT NULL,
  `loai_la_cay` varchar(1024) DEFAULT NULL,
  `note` text NOT NULL,
  `active` tinyint(4) NOT NULL DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_mt1_dhnv` */


/*Table structure for table `viem_gan_mt1_kpa_not` */

DROP TABLE IF EXISTS `viem_gan_mt1_kpa_not`;

CREATE TABLE `viem_gan_mt1_kpa_not` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL,
  `time` datetime NOT NULL,
  `nd_duong_th_sang` varchar(255) DEFAULT NULL,
  `nd_duong_th_trua` varchar(255) DEFAULT NULL,
  `nd_duong_th_toi` varchar(255) DEFAULT NULL,
  `nd_duong_th_bua_phu` varchar(255) DEFAULT NULL,
  `nd_tinh_mac` varchar(255) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_mt1_kpa_not` */

/*Table structure for table `viem_gan_mt1_sga` */

DROP TABLE IF EXISTS `viem_gan_mt1_sga`;

CREATE TABLE `viem_gan_mt1_sga` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) DEFAULT NULL,
  `time_id` bigint(20) DEFAULT NULL,
  `cn_6_thang` tinyint(4) DEFAULT NULL,
  `cn_2_tuan` tinyint(4) DEFAULT NULL,
  `khau_phan_an_ht` tinyint(4) DEFAULT NULL,
  `tieu_chung_th` tinyint(4) DEFAULT NULL,
  `giam_chuc_nang` tinyint(4) DEFAULT NULL,
  `nc_chuyen_hoa` tinyint(4) DEFAULT NULL,
  `mo_duoi_da` tinyint(4) DEFAULT NULL,
  `teo_co` tinyint(4) DEFAULT NULL,
  `phu` tinyint(4) DEFAULT NULL,
  `co_chuong` tinyint(4) DEFAULT NULL,
  `phan_loai` tinyint(4) DEFAULT NULL,
  `total_point` int(11) DEFAULT NULL,
  `active` tinyint(4) NOT NULL DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_mt1_sga` */


/*Table structure for table `viem_gan_mt1_so_gan` */

DROP TABLE IF EXISTS `viem_gan_mt1_so_gan`;

CREATE TABLE `viem_gan_mt1_so_gan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL,
  `tinh_trang_gan` tinyint(4) DEFAULT NULL,
  `muc_do_xo_gan` tinyint(4) DEFAULT NULL,
  `albumin` varchar(50) DEFAULT NULL,
  `tu_van_dd` tinyint(4) DEFAULT NULL,
  `so_bua_moi_ngay` tinyint(4) DEFAULT NULL,
  `bua_dem` tinyint(4) DEFAULT NULL,
  `benh_ly_kem_theo` varchar(255) DEFAULT NULL,
  `benh_ly_kem_theo_khac` varchar(512) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_mt1_so_gan` */

/*Table structure for table `viem_gan_sga` */

DROP TABLE IF EXISTS `viem_gan_sga`;

CREATE TABLE `viem_gan_sga` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time_id` bigint(20) NOT NULL,
  `cn_6_thang` tinyint(4) DEFAULT NULL,
  `cn_2_tuan` tinyint(4) DEFAULT NULL,
  `khau_phan_an_ht` tinyint(4) DEFAULT NULL,
  `tieu_chung_th` tinyint(4) DEFAULT NULL,
  `giam_chuc_nang` tinyint(4) DEFAULT NULL,
  `nc_chuyen_hoa` tinyint(4) DEFAULT NULL,
  `mo_duoi_da` tinyint(4) DEFAULT NULL,
  `teo_co` tinyint(4) DEFAULT NULL,
  `phu` tinyint(4) DEFAULT NULL,
  `co_chuong` tinyint(4) DEFAULT NULL,
  `phan_loai` tinyint(4) DEFAULT NULL,
  `total_point` int(11) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_sga` */

/*Table structure for table `viem_gan_td_ngt` */

DROP TABLE IF EXISTS `viem_gan_td_ngt`;

CREATE TABLE `viem_gan_td_ngt` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `cn` float DEFAULT NULL,
  `bat_thuong` text DEFAULT NULL,
  `tu_van` text DEFAULT NULL,
  `note` text DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_td_ngt` */
/*Table structure for table `viem_gan_td_not` */

DROP TABLE IF EXISTS `viem_gan_td_not`;

CREATE TABLE `viem_gan_td_not` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `nd_duong_th` text DEFAULT NULL,
  `nd_tinh_mac` text DEFAULT NULL,
  `note` text DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_td_not` */
/*Table structure for table `viem_gan_tqau` */

DROP TABLE IF EXISTS `viem_gan_tqau`;

CREATE TABLE `viem_gan_tqau` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `bua_chinh` tinyint(4) DEFAULT NULL,
  `bua_phu` tinyint(4) DEFAULT NULL,
  `bua_phu_an` varchar(255) DEFAULT NULL,
  `bua_phu_an_khac` varchar(1024) DEFAULT NULL,
  `an_kieng` tinyint(4) DEFAULT NULL,
  `an_kieng_loai` varchar(255) DEFAULT NULL,
  `an_kieng_loai_khac` varchar(1024) DEFAULT NULL,
  `ruou_bia` tinyint(4) DEFAULT NULL,
  `ruou_bia_ts` tinyint(4) DEFAULT NULL,
  `ml_ruou` int(11) DEFAULT NULL,
  `ml_bia` int(11) DEFAULT NULL,
  `do_uong_khac` tinyint(4) DEFAULT NULL,
  `do_uong_khac_ts` tinyint(4) DEFAULT NULL,
  `loai_do_uong` varchar(255) DEFAULT NULL,
  `loai_do_uong_khac` varchar(1024) DEFAULT NULL,
  `su_dung_la_cay` tinyint(4) DEFAULT NULL,
  `loai_la_cay` varchar(1024) DEFAULT NULL,
  `cham_soc_dd` tinyint(4) DEFAULT NULL,
  `cham_soc_dd_khac` varchar(1024) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_tqau` */

/*Table structure for table `viem_gan_ttdd` */

DROP TABLE IF EXISTS `viem_gan_ttdd`;

CREATE TABLE `viem_gan_ttdd` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` bigint(20) NOT NULL,
  `time_id` bigint(20) NOT NULL,
  `cn` varchar(128) DEFAULT NULL,
  `cc` varchar(128) DEFAULT NULL,
  `vong_bap_chan` varchar(128) DEFAULT NULL,
  `glucose` varchar(128) DEFAULT NULL,
  `ure` varchar(128) DEFAULT NULL,
  `creatinin` varchar(128) DEFAULT NULL,
  `got` varchar(128) DEFAULT NULL,
  `gpt` varchar(128) DEFAULT NULL,
  `ggt` varchar(128) DEFAULT NULL,
  `hong_cau` varchar(128) DEFAULT NULL,
  `hemoglobin` varchar(128) DEFAULT NULL,
  `pre_albumin` varchar(128) DEFAULT NULL,
  `albumin` varchar(128) DEFAULT NULL,
  `protein_tp` varchar(128) DEFAULT NULL,
  `sat_huyet_thanh` varchar(128) DEFAULT NULL,
  `ferritin` varchar(128) DEFAULT NULL,
  `active` tinyint(4) DEFAULT 1,
  `created_by` bigint(20) DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Data for the table `viem_gan_ttdd` */

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
