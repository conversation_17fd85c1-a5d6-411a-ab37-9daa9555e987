-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2deb1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Sep 02, 2025 at 01:39 AM
-- Server version: 8.4.6-0ubuntu0.25.04.2
-- PHP Version: 8.4.5

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `patients`
--

-- --------------------------------------------------------

--
-- Table structure for table `cat_gan_nho_kpa`
--

CREATE TABLE `cat_gan_nho_kpa` (
  `id` bigint UNSIGNED NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `nd_duong_th` text COLLATE utf8mb4_general_ci,
  `nd_tinh_mac` text COLLATE utf8mb4_general_ci,
  `note` text COLLATE utf8mb4_general_ci,
  `xet_nghiem` text COLLATE utf8mb4_general_ci NOT NULL,
  `y_kien_bs` text COLLATE utf8mb4_general_ci,
  `active` tinyint NOT NULL DEFAULT '1',
  `campaign_id` bigint DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `patients`
--

CREATE TABLE `patients` (
  `id` bigint NOT NULL,
  `fullname` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ngay_nhap_vien` timestamp NULL DEFAULT NULL,
  `ma_benh_an` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phong_dieu_tri` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dan_toc` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dan_toc_khac` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `trinh_do` tinyint DEFAULT NULL,
  `nghe_nghiep` tinyint DEFAULT NULL,
  `nghe_nghiep_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `noi_o` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `xep_loai_kt` tinyint DEFAULT NULL COMMENT 'xep loai kinh te',
  `chuan_doan` text COLLATE utf8mb4_general_ci,
  `khoa` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `que_quan` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` tinyint NOT NULL COMMENT '3 viem gan 4 uon van 5 cat gan',
  `moi_quan_he` text COLLATE utf8mb4_general_ci,
  `dieu_tra_vien` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tien_su_benh` text COLLATE utf8mb4_general_ci,
  `cn` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cc` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `bien_ban` tinyint DEFAULT '0',
  `khan_cap` tinyint DEFAULT '0',
  `ngay_hoi_chan` timestamp NULL DEFAULT NULL,
  `ngay_dieu_tra` timestamp NULL DEFAULT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `patients_research`
--

CREATE TABLE `patients_research` (
  `id` bigint UNSIGNED NOT NULL,
  `fullname` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `address` varchar(3072) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ngay_nhap_vien` timestamp NULL DEFAULT NULL,
  `ma_benh_an` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phong_dieu_tri` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dan_toc` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dan_toc_khac` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `trinh_do` tinyint DEFAULT NULL,
  `nghe_nghiep` tinyint DEFAULT NULL,
  `nghe_nghiep_khac` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `noi_o` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `xep_loai_kt` tinyint DEFAULT NULL,
  `chuan_doan` text COLLATE utf8mb4_general_ci,
  `khoa` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `que_quan` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `id_research` bigint DEFAULT NULL,
  `menu_example` longtext COLLATE utf8mb4_general_ci,
  `active` tinyint NOT NULL DEFAULT '1',
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `phieu_hoi_chan_danh_gia`
--

CREATE TABLE `phieu_hoi_chan_danh_gia` (
  `id` bigint UNSIGNED NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `time_id` bigint DEFAULT NULL,
  `tinh_trang_nguoi_benh` text COLLATE utf8mb4_general_ci,
  `khau_phan_an_24h` text COLLATE utf8mb4_general_ci,
  `tieu_hoa` text COLLATE utf8mb4_general_ci,
  `danh_gia` text COLLATE utf8mb4_general_ci,
  `ket_qua_can_lam_sang` text COLLATE utf8mb4_general_ci,
  `can_thiep_kcal` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `can_thiep_kg` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `can_thiep_note` text COLLATE utf8mb4_general_ci,
  `che_do_dinh_duong` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `che_do_dinh_duong_note` text COLLATE utf8mb4_general_ci,
  `bo_sung` text COLLATE utf8mb4_general_ci,
  `chu_y` text COLLATE utf8mb4_general_ci,
  `active` tinyint DEFAULT '1',
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `phieu_hoi_chan_ttc`
--

CREATE TABLE `phieu_hoi_chan_ttc` (
  `id` bigint UNSIGNED NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `cn` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cc` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ctc` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `chuan_doan_ls` text COLLATE utf8mb4_general_ci,
  `ngay_hoi_chan` date DEFAULT NULL,
  `cn_1_thang` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `khau_phan_an` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `trieu_chung_th` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `giam_chuc_nang_hd` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nhu_cau_chuyen_hoa` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `kham_lam_sang` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `chon_tt_1` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tien_su_benh` text COLLATE utf8mb4_general_ci,
  `tinh_trang_nguoi_benh` text COLLATE utf8mb4_general_ci,
  `khau_phan_an_24h` text COLLATE utf8mb4_general_ci,
  `tieu_hoa` text COLLATE utf8mb4_general_ci,
  `che_do_dinh_duong` varchar(96) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `che_do_dinh_duong_note` text COLLATE utf8mb4_general_ci,
  `duong_nuoi` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dich_vao` int DEFAULT NULL,
  `dich_ra` int DEFAULT NULL,
  `e_nckn` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `can_thiep_kcal` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `can_thiep_kg` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `can_thiep_note` text COLLATE utf8mb4_general_ci,
  `ket_qua_can_lam_sang` text COLLATE utf8mb4_general_ci,
  `bo_sung` text COLLATE utf8mb4_general_ci,
  `chu_y` text COLLATE utf8mb4_general_ci,
  `campaign_id` int DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `research`
--

CREATE TABLE `research` (
  `id` bigint NOT NULL,
  `name` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `note` text COLLATE utf8mb4_general_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `research`
--

INSERT INTO `research` (`id`, `name`, `created_at`, `updated_at`, `campaign_id`, `created_by`, `active`, `note`) VALUES
(3, 'TTDD xơ gan', '2025-08-11 08:01:54', '2025-08-11 08:01:54', 1, 2, 1, 'Hường'),
(4, 'TTDD xơ gan', '2025-08-18 04:56:20', '2025-08-18 04:56:20', 8, 2, 1, 'Hường'),
(5, 'TTDD bệnh gan', '2025-08-19 08:31:25', '2025-08-19 08:31:25', 2, 2, 1, 'Thơm'),
(6, 'Đề tài NCS', '2025-08-19 08:36:18', '2025-08-19 08:36:18', 9, 3, 1, 'Thơm'),
(7, 'Đề tài NCS', '2025-08-19 08:37:44', '2025-08-19 08:37:44', 1, 2, 1, 'Thơm'),
(8, 'TTDD xơ gan', '2025-08-25 10:56:56', '2025-08-25 10:56:56', 3, 2, 1, 'Hường');

-- --------------------------------------------------------

--
-- Table structure for table `times`
--

CREATE TABLE `times` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `type` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '1 tinh trang dd 2 can thiep dd 3 sga',
  `active` tinyint DEFAULT '1',
  `project` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `uon_van_kpa`
--

CREATE TABLE `uon_van_kpa` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `nd_duong_th` text COLLATE utf8mb4_general_ci,
  `nd_tinh_mac` text COLLATE utf8mb4_general_ci,
  `note` text COLLATE utf8mb4_general_ci,
  `campaign_id` int DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `uon_van_ls`
--

CREATE TABLE `uon_van_ls` (
  `id` bigint NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `time_id` bigint DEFAULT NULL,
  `cn` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vong_bap_chan` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cc` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `albumin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pre_albumin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `hemoglobin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `protein` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phospho` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `glucose` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `magie` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `kali` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ck` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ure` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `bilirubin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `creatinin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `benh_ly` text COLLATE utf8mb4_general_ci,
  `thuoc` text COLLATE utf8mb4_general_ci,
  `active` tinyint DEFAULT '1',
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `uon_van_med`
--

CREATE TABLE `uon_van_med` (
  `id` bigint NOT NULL,
  `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL,
  `type` varchar(128) COLLATE utf8mb4_general_ci NOT NULL,
  `active` tinyint DEFAULT '1',
  `campaign_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `uon_van_med`
--

INSERT INTO `uon_van_med` (`id`, `name`, `type`, `active`, `campaign_id`) VALUES
(1, 'Seduxen', 'an-than-gc', 1, 1),
(2, 'Diazepam', 'an-than-gc', 1, 1),
(3, 'Midazolam', 'an-than-gc', 1, 1),
(4, 'Fentanyl', 'an-than-gc', 1, 1),
(5, 'Propofol', 'an-than-gc', 1, 1),
(6, 'Atracurium', 'an-than-gc', 1, 1),
(7, 'Rocuronium', 'an-than-gc', 1, 1),
(8, 'Lactulose', 'nhuan-trang', 1, 1),
(9, 'Sorbitol', 'nhuan-trang', 1, 1),
(10, 'Metoclopramide', 'tang-ndr', 1, 1);

-- --------------------------------------------------------

--
-- Table structure for table `uon_van_sga`
--

CREATE TABLE `uon_van_sga` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time_id` bigint NOT NULL,
  `cn_6_thang` tinyint DEFAULT NULL,
  `cn_2_tuan` tinyint DEFAULT NULL,
  `khau_phan_an_ht` tinyint DEFAULT NULL,
  `tieu_chung_th` tinyint DEFAULT NULL,
  `giam_chuc_nang` tinyint DEFAULT NULL,
  `nc_chuyen_hoa` tinyint DEFAULT NULL,
  `mo_duoi_da` tinyint DEFAULT NULL,
  `teo_co` tinyint DEFAULT NULL,
  `phu` tinyint DEFAULT NULL,
  `co_chuong` tinyint DEFAULT NULL,
  `phan_loai` tinyint DEFAULT NULL,
  `total_point` int DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `uon_van_ttth`
--

CREATE TABLE `uon_van_ttth` (
  `id` bigint NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `time_id` bigint DEFAULT NULL,
  `chuong_bung` tinyint DEFAULT NULL,
  `trao_nguoc` tinyint DEFAULT NULL,
  `tao_bon` tinyint DEFAULT NULL,
  `phan_long_3_ngay` tinyint DEFAULT NULL,
  `duong_mau_10` tinyint DEFAULT NULL,
  `duong_mau_20` tinyint DEFAULT NULL,
  `so_lan_di_ngoai` int DEFAULT NULL,
  `tinh_trang_phan` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dich_ton_du` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `campaign_id` int DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `viem_gam_ttcb`
--

CREATE TABLE `viem_gam_ttcb` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `so_lan_vgc` tinyint DEFAULT NULL COMMENT 'so lan dieu tri vgc',
  `thoi_gian_vgm` tinyint DEFAULT NULL COMMENT 'thoi gian mac vg man',
  `thoi_gian_vg_ruou` tinyint DEFAULT NULL COMMENT 'thoi giam mac vg ruou',
  `thoi_gian_vg_virus` tinyint DEFAULT NULL COMMENT 'thoi giam mac vg virus',
  `benh_gan_mat_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `thoi_gian_gm_khac` tinyint DEFAULT NULL,
  `ts_benh_khac_1` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ts_benh_1_so_nam` tinyint DEFAULT NULL,
  `ts_benh_khac_2` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ts_benh_2_so_nam` tinyint DEFAULT NULL,
  `ts_benh_khac_3` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ts_benh_3_so_nam` tinyint DEFAULT NULL,
  `ts_benh_khac_4` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ts_benh_4_so_nam` tinyint DEFAULT NULL,
  `ts_benh_khac_5` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ts_benh_5_so_nam` tinyint DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `viem_gam_ttcb`
--

INSERT INTO `viem_gam_ttcb` (`id`, `patient_id`, `so_lan_vgc`, `thoi_gian_vgm`, `thoi_gian_vg_ruou`, `thoi_gian_vg_virus`, `benh_gan_mat_khac`, `thoi_gian_gm_khac`, `ts_benh_khac_1`, `ts_benh_1_so_nam`, `ts_benh_khac_2`, `ts_benh_2_so_nam`, `ts_benh_khac_3`, `ts_benh_3_so_nam`, `ts_benh_khac_4`, `ts_benh_4_so_nam`, `ts_benh_khac_5`, `ts_benh_5_so_nam`, `created_by`, `campaign_id`, `created_at`, `updated_at`) VALUES
(4, 6, 1, 2, 3, 4, 'ko', 1, 'đái tháo đường', 1, 'tăng huyết áp', 1, 'ko', 1, 'ko', 1, 'ko', 1, 1, 1, '2025-07-11 08:00:20', '2025-01-13 07:27:04'),
(5, 19, 0, 0, 0, 1, '0', 0, 'u lympho non hogkin', 5, '', 0, '', 0, '', 0, '', 0, 3, 1, '2025-07-11 08:00:20', '2025-02-20 09:10:11'),
(6, 20, 0, 0, 0, 0, '', 0, '', 0, '', 0, '', 0, '', 0, '', 0, 1, 1, '2025-07-11 08:00:20', '2025-02-20 09:30:36'),
(7, 254, 0, 0, 0, 0, '', 0, '', 0, '', 0, '', 0, '', 0, '', 0, 2, NULL, '2025-08-04 15:30:22', '2025-08-04 15:30:22'),
(8, 271, 0, 0, 0, 0, '', 0, '', 0, '', 0, '', 0, '', 0, '', 0, 2, NULL, '2025-08-10 00:40:58', '2025-08-10 00:40:58'),
(9, 296, 0, 0, 0, 0, '', 0, '', 0, '', 0, '', 0, '', 0, '', 0, 2, NULL, '2025-08-18 04:26:19', '2025-08-18 04:26:19'),
(10, 318, 0, 0, 0, 0, '', 0, '', 0, '', 0, '', 0, '', 0, '', 0, 2, NULL, '2025-08-19 14:54:47', '2025-08-19 14:54:47');

-- --------------------------------------------------------

--
-- Table structure for table `viem_gan_ctdd`
--

CREATE TABLE `viem_gan_ctdd` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time_id` bigint NOT NULL,
  `chan_an` tinyint DEFAULT NULL,
  `chan_an_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `an_khong_ngon` tinyint DEFAULT NULL,
  `an_khong_ngon_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `buon_non` tinyint DEFAULT NULL,
  `buon_non_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `non` tinyint DEFAULT NULL,
  `non_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tao_bon` tinyint DEFAULT NULL,
  `tao_bon_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tieu_chay` tinyint DEFAULT NULL,
  `tieu_chay_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `song_phan` tinyint DEFAULT NULL,
  `song_phan_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nhiet_mieng` tinyint DEFAULT NULL,
  `nhiet_mieng_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `thay_doi_vi_giac` tinyint DEFAULT NULL,
  `thay_doi_vi_giac_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `khac` tinyint DEFAULT NULL,
  `khac_note` text COLLATE utf8mb4_general_ci,
  `co_chuong` tinyint DEFAULT NULL,
  `co_chuong_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `met_moi` tinyint DEFAULT NULL,
  `met_moi_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dau` tinyint DEFAULT NULL,
  `dau_note` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `viem_gan_ctdd`
--

INSERT INTO `viem_gan_ctdd` (`id`, `patient_id`, `time_id`, `chan_an`, `chan_an_note`, `an_khong_ngon`, `an_khong_ngon_note`, `buon_non`, `buon_non_note`, `non`, `non_note`, `tao_bon`, `tao_bon_note`, `tieu_chay`, `tieu_chay_note`, `song_phan`, `song_phan_note`, `nhiet_mieng`, `nhiet_mieng_note`, `thay_doi_vi_giac`, `thay_doi_vi_giac_note`, `khac`, `khac_note`, `co_chuong`, `co_chuong_note`, `met_moi`, `met_moi_note`, `dau`, `dau_note`, `active`, `created_by`, `campaign_id`, `created_at`, `updated_at`) VALUES
(4, 19, 85, 1, '', 1, '', 1, '', 1, '', 1, '', 2, '', 2, '', 2, '', 1, '', NULL, '', 2, '', 1, '', 2, '', 1, 2, 1, '2025-07-11 08:00:20', '2025-02-20 09:12:38');

-- --------------------------------------------------------

--
-- Table structure for table `viem_gan_dhnv`
--

CREATE TABLE `viem_gan_dhnv` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `chan_an_met_moi` tinyint DEFAULT NULL,
  `bieu_hien_tieu_hoa` tinyint DEFAULT NULL,
  `bieu_hien_tieu_hoa_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dau_tuc_hsp` tinyint DEFAULT NULL,
  `dau_tuc_hsp_khi` tinyint DEFAULT NULL,
  `dau_tuc_hsp_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vang_da_vang_mat` tinyint DEFAULT NULL,
  `bieu_hien_phu` tinyint DEFAULT NULL,
  `bieu_hien_co_chuong` tinyint DEFAULT NULL,
  `ngua_da` tinyint DEFAULT NULL,
  `ngua_da_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `xuat_huyet_tieu_hoa` tinyint DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `viem_gan_dhnv`
--

INSERT INTO `viem_gan_dhnv` (`id`, `patient_id`, `chan_an_met_moi`, `bieu_hien_tieu_hoa`, `bieu_hien_tieu_hoa_khac`, `dau_tuc_hsp`, `dau_tuc_hsp_khi`, `dau_tuc_hsp_khac`, `vang_da_vang_mat`, `bieu_hien_phu`, `bieu_hien_co_chuong`, `ngua_da`, `ngua_da_khac`, `xuat_huyet_tieu_hoa`, `active`, `created_by`, `campaign_id`, `created_at`, `updated_at`) VALUES
(4, 6, 1, 1, NULL, 1, 1, NULL, 1, 3, 1, 1, '', 1, 1, 1, 1, '2025-07-11 08:00:20', '2025-01-13 07:27:26'),
(5, 296, 0, 0, NULL, NULL, 0, NULL, NULL, 0, NULL, 0, '', NULL, 1, 2, 8, '2025-08-18 04:30:15', '2025-08-18 04:30:15');

-- --------------------------------------------------------

--
-- Table structure for table `viem_gan_mt1_dhnv`
--

CREATE TABLE `viem_gan_mt1_dhnv` (
  `id` bigint UNSIGNED NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `chan_doan_benh` tinyint DEFAULT NULL,
  `nguyen_nhan` tinyint DEFAULT NULL,
  `nguyen_nhan_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cn` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cc` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vong_bap_chan` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `got` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gpt` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `hemoglobin` varchar(384) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `bua_chinh` tinyint DEFAULT NULL,
  `bua_phu` tinyint DEFAULT NULL,
  `bua_phu_an` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `bua_phu_an_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `an_kieng` tinyint DEFAULT NULL,
  `an_kieng_loai` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `an_kieng_loai_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ruou_bia` tinyint DEFAULT NULL,
  `ruou_bia_ts` tinyint DEFAULT NULL,
  `ml_ruou` int DEFAULT NULL,
  `ml_bia` int DEFAULT NULL,
  `do_uong_khac` tinyint DEFAULT NULL,
  `do_uong_khac_ts` tinyint DEFAULT NULL,
  `loai_do_uong` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `loai_do_uong_khac` varchar(3072) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `su_dung_la_cay` tinyint DEFAULT NULL,
  `loai_la_cay` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_general_ci NOT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_mt1_kpa_not`
--

CREATE TABLE `viem_gan_mt1_kpa_not` (
  `id` int NOT NULL,
  `patient_id` int NOT NULL,
  `time` datetime NOT NULL,
  `nd_duong_th_sang` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nd_duong_th_trua` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nd_duong_th_toi` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nd_duong_th_bua_phu` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nd_tinh_mac` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `note` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `campaign_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_mt1_sga`
--

CREATE TABLE `viem_gan_mt1_sga` (
  `id` bigint UNSIGNED NOT NULL,
  `patient_id` bigint DEFAULT NULL,
  `time_id` bigint DEFAULT NULL,
  `cn_6_thang` tinyint DEFAULT NULL,
  `cn_2_tuan` tinyint DEFAULT NULL,
  `khau_phan_an_ht` tinyint DEFAULT NULL,
  `tieu_chung_th` tinyint DEFAULT NULL,
  `giam_chuc_nang` tinyint DEFAULT NULL,
  `nc_chuyen_hoa` tinyint DEFAULT NULL,
  `mo_duoi_da` tinyint DEFAULT NULL,
  `teo_co` tinyint DEFAULT NULL,
  `phu` tinyint DEFAULT NULL,
  `co_chuong` tinyint DEFAULT NULL,
  `phan_loai` tinyint DEFAULT NULL,
  `total_point` int DEFAULT NULL,
  `active` tinyint NOT NULL DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


--
-- Table structure for table `viem_gan_mt1_so_gan`
--

CREATE TABLE `viem_gan_mt1_so_gan` (
  `id` int NOT NULL,
  `patient_id` int NOT NULL,
  `tinh_trang_gan` tinyint DEFAULT NULL,
  `muc_do_xo_gan` tinyint DEFAULT NULL,
  `albumin` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tu_van_dd` tinyint DEFAULT NULL,
  `so_bua_moi_ngay` tinyint DEFAULT NULL,
  `bua_dem` tinyint DEFAULT NULL,
  `benh_ly_kem_theo` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `benh_ly_kem_theo_khac` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `campaign_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_sga`
--

CREATE TABLE `viem_gan_sga` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time_id` bigint NOT NULL,
  `cn_6_thang` tinyint DEFAULT NULL,
  `cn_2_tuan` tinyint DEFAULT NULL,
  `khau_phan_an_ht` tinyint DEFAULT NULL,
  `tieu_chung_th` tinyint DEFAULT NULL,
  `giam_chuc_nang` tinyint DEFAULT NULL,
  `nc_chuyen_hoa` tinyint DEFAULT NULL,
  `mo_duoi_da` tinyint DEFAULT NULL,
  `teo_co` tinyint DEFAULT NULL,
  `phu` tinyint DEFAULT NULL,
  `co_chuong` tinyint DEFAULT NULL,
  `phan_loai` tinyint DEFAULT NULL,
  `total_point` int DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_td_ngt`
--

CREATE TABLE `viem_gan_td_ngt` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `cn` float DEFAULT NULL,
  `bat_thuong` text COLLATE utf8mb4_general_ci,
  `tu_van` text COLLATE utf8mb4_general_ci,
  `note` text COLLATE utf8mb4_general_ci,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_td_not`
--

CREATE TABLE `viem_gan_td_not` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `nd_duong_th` text COLLATE utf8mb4_general_ci,
  `nd_tinh_mac` text COLLATE utf8mb4_general_ci,
  `note` text COLLATE utf8mb4_general_ci,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_tqau`
--

CREATE TABLE `viem_gan_tqau` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `bua_chinh` tinyint DEFAULT NULL,
  `bua_phu` tinyint DEFAULT NULL,
  `bua_phu_an` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `bua_phu_an_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `an_kieng` tinyint DEFAULT NULL,
  `an_kieng_loai` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `an_kieng_loai_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ruou_bia` tinyint DEFAULT NULL,
  `ruou_bia_ts` tinyint DEFAULT NULL,
  `ml_ruou` int DEFAULT NULL,
  `ml_bia` int DEFAULT NULL,
  `do_uong_khac` tinyint DEFAULT NULL,
  `do_uong_khac_ts` tinyint DEFAULT NULL,
  `loai_do_uong` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `loai_do_uong_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `su_dung_la_cay` tinyint DEFAULT NULL,
  `loai_la_cay` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cham_soc_dd` tinyint DEFAULT NULL,
  `cham_soc_dd_khac` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `viem_gan_ttdd`
--

CREATE TABLE `viem_gan_ttdd` (
  `id` bigint NOT NULL,
  `patient_id` bigint NOT NULL,
  `time_id` bigint NOT NULL,
  `cn` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cc` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vong_bap_chan` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `glucose` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ure` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `creatinin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `got` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gpt` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ggt` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `hong_cau` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `hemoglobin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pre_albumin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `albumin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `protein_tp` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sat_huyet_thanh` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ferritin` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint DEFAULT '1',
  `created_by` bigint DEFAULT NULL,
  `campaign_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;