const axios = require('axios');
const crypto = require('crypto');
const commonService = require('./commonService');

class WebhookService {
    constructor() {
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
        this.timeout = 10000; // 10 seconds
    }
    
    /**
     * Send webhook notification
     */
    async sendWebhook(webhookConfig, eventData) {
        const {
            url,
            secret,
            headers = {},
            method = 'POST',
            retryAttempts = this.retryAttempts
        } = webhookConfig;
        
        if (!url) {
            throw new Error('Webhook URL is required');
        }
        
        // Prepare payload
        const payload = {
            event: eventData.event,
            timestamp: new Date().toISOString(),
            data: eventData.data
        };
        
        // Generate signature if secret is provided
        const signature = secret ? this.generateSignature(payload, secret) : null;
        
        // Prepare headers
        const requestHeaders = {
            'Content-Type': 'application/json',
            'User-Agent': 'Survey-System-Webhook/1.0',
            ...headers
        };
        
        if (signature) {
            requestHeaders['X-Webhook-Signature'] = signature;
        }
        
        // Send webhook with retry logic
        return await this.sendWithRetry({
            url,
            method,
            headers: requestHeaders,
            data: payload,
            timeout: this.timeout
        }, retryAttempts);
    }
    
    /**
     * Send webhook with retry logic
     */
    async sendWithRetry(requestConfig, retryAttempts) {
        let lastError;
        
        for (let attempt = 1; attempt <= retryAttempts; attempt++) {
            try {
                const response = await axios(requestConfig);
                
                // Log successful webhook
                await this.logWebhook({
                    url: requestConfig.url,
                    method: requestConfig.method,
                    payload: requestConfig.data,
                    response: {
                        status: response.status,
                        statusText: response.statusText,
                        data: response.data
                    },
                    attempt,
                    success: true
                });
                
                return {
                    success: true,
                    status: response.status,
                    data: response.data,
                    attempt
                };
                
            } catch (error) {
                lastError = error;
                
                // Log failed attempt
                await this.logWebhook({
                    url: requestConfig.url,
                    method: requestConfig.method,
                    payload: requestConfig.data,
                    error: {
                        message: error.message,
                        code: error.code,
                        status: error.response?.status,
                        statusText: error.response?.statusText
                    },
                    attempt,
                    success: false
                });
                
                // Don't retry on client errors (4xx)
                if (error.response && error.response.status >= 400 && error.response.status < 500) {
                    break;
                }
                
                // Wait before retry (except on last attempt)
                if (attempt < retryAttempts) {
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
                }
            }
        }
        
        return {
            success: false,
            error: lastError.message,
            status: lastError.response?.status,
            attempts: retryAttempts
        };
    }
    
    /**
     * Generate webhook signature
     */
    generateSignature(payload, secret) {
        const payloadString = JSON.stringify(payload);
        return crypto
            .createHmac('sha256', secret)
            .update(payloadString)
            .digest('hex');
    }
    
    /**
     * Verify webhook signature
     */
    verifySignature(payload, signature, secret) {
        const expectedSignature = this.generateSignature(payload, secret);
        return crypto.timingSafeEqual(
            Buffer.from(signature, 'hex'),
            Buffer.from(expectedSignature, 'hex')
        );
    }
    
    /**
     * Send survey response webhook
     */
    async sendSurveyResponseWebhook(surveyConfig, responseData) {
        if (!surveyConfig.webhook_url) {
            return { success: true, message: 'No webhook configured' };
        }
        
        const webhookConfig = {
            url: surveyConfig.webhook_url,
            secret: surveyConfig.webhook_secret,
            headers: surveyConfig.webhook_headers ? JSON.parse(surveyConfig.webhook_headers) : {}
        };
        
        const eventData = {
            event: 'survey.response.created',
            data: {
                survey: {
                    id: surveyConfig.id,
                    name: surveyConfig.name,
                    slug: surveyConfig.survey_url_slug
                },
                response: responseData,
                metadata: {
                    ip_address: responseData.ip_address,
                    user_agent: responseData.user_agent,
                    submitted_at: responseData.submitted_at
                }
            }
        };
        
        return await this.sendWebhook(webhookConfig, eventData);
    }
    
    /**
     * Send survey completion webhook
     */
    async sendSurveyCompletionWebhook(surveyConfig, statistics) {
        if (!surveyConfig.webhook_url) {
            return { success: true, message: 'No webhook configured' };
        }
        
        const webhookConfig = {
            url: surveyConfig.webhook_url,
            secret: surveyConfig.webhook_secret,
            headers: surveyConfig.webhook_headers ? JSON.parse(surveyConfig.webhook_headers) : {}
        };
        
        const eventData = {
            event: 'survey.completed',
            data: {
                survey: {
                    id: surveyConfig.id,
                    name: surveyConfig.name,
                    slug: surveyConfig.survey_url_slug
                },
                statistics: statistics,
                completed_at: new Date().toISOString()
            }
        };
        
        return await this.sendWebhook(webhookConfig, eventData);
    }
    
    /**
     * Send custom webhook
     */
    async sendCustomWebhook(webhookConfig, eventType, data) {
        const eventData = {
            event: eventType,
            data: data
        };
        
        return await this.sendWebhook(webhookConfig, eventData);
    }
    
    /**
     * Test webhook endpoint
     */
    async testWebhook(webhookConfig) {
        const eventData = {
            event: 'webhook.test',
            data: {
                message: 'This is a test webhook from Survey System',
                timestamp: new Date().toISOString()
            }
        };
        
        return await this.sendWebhook(webhookConfig, eventData);
    }
    
    /**
     * Log webhook activity
     */
    async logWebhook(logData) {
        try {
            const logEntry = {
                id: commonService.generateUUID(),
                webhook_url: logData.url,
                method: logData.method,
                payload: JSON.stringify(logData.payload),
                response_status: logData.response?.status || null,
                response_data: logData.response ? JSON.stringify(logData.response.data) : null,
                error_message: logData.error?.message || null,
                error_code: logData.error?.code || null,
                attempt_number: logData.attempt,
                success: logData.success,
                created_at: new Date(),
                updated_at: new Date()
            };
            
            await commonService.insertRecordTable(logEntry, 'webhook_logs');
        } catch (error) {
            console.error('Failed to log webhook activity:', error.message);
        }
    }
    
    /**
     * Get webhook logs
     */
    async getWebhookLogs(filters = {}) {
        try {
            const whereConditions = [];
            const params = [];
            
            if (filters.webhook_url) {
                whereConditions.push('webhook_url = ?');
                params.push(filters.webhook_url);
            }
            
            if (filters.success !== undefined) {
                whereConditions.push('success = ?');
                params.push(filters.success);
            }
            
            if (filters.start_date) {
                whereConditions.push('created_at >= ?');
                params.push(filters.start_date);
            }
            
            if (filters.end_date) {
                whereConditions.push('created_at <= ?');
                params.push(filters.end_date);
            }
            
            const whereClause = whereConditions.length > 0 
                ? `WHERE ${whereConditions.join(' AND ')}`
                : '';
            
            const query = `
                SELECT * FROM webhook_logs 
                ${whereClause}
                ORDER BY created_at DESC
                LIMIT ${filters.limit || 100}
            `;
            
            return await commonService.executeQuery(query, params);
        } catch (error) {
            console.error('Failed to get webhook logs:', error.message);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Clean up old webhook logs
     */
    async cleanupLogs(daysToKeep = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            
            const result = await commonService.executeQuery(
                'DELETE FROM webhook_logs WHERE created_at < ?',
                [cutoffDate.toISOString()]
            );
            
            console.log(`Cleaned up ${result.changes || 0} old webhook logs`);
            return { success: true, deletedCount: result.changes || 0 };
        } catch (error) {
            console.error('Failed to cleanup webhook logs:', error.message);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Get webhook statistics
     */
    async getWebhookStats(webhookUrl, days = 7) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            
            const query = `
                SELECT 
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_requests,
                    AVG(CASE WHEN success = 1 THEN 1.0 ELSE 0.0 END) * 100 as success_rate,
                    MAX(attempt_number) as max_attempts
                FROM webhook_logs 
                WHERE webhook_url = ? AND created_at >= ?
            `;
            
            const result = await commonService.executeQuery(query, [webhookUrl, startDate.toISOString()]);
            
            if (result.success && result.data && result.data.length > 0) {
                return {
                    success: true,
                    stats: result.data[0]
                };
            }
            
            return {
                success: true,
                stats: {
                    total_requests: 0,
                    successful_requests: 0,
                    failed_requests: 0,
                    success_rate: 0,
                    max_attempts: 0
                }
            };
        } catch (error) {
            console.error('Failed to get webhook stats:', error.message);
            return { success: false, error: error.message };
        }
    }
}

module.exports = new WebhookService();
