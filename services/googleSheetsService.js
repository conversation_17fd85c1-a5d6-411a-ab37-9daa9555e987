const { JWT } = require('google-auth-library');
const { google } = require('googleapis');

const googleSheetsService = {
    /**
     * Tạo service account JWT để authenticate với Google Sheets API
     * @returns {JWT} - JWT service account
     */
    createServiceAccountAuth: () => {
        try {
            // Cấu hình service account từ environment variables
            const serviceAccountAuth = new JWT({
                email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
                key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
                scopes: [
                    'https://www.googleapis.com/auth/spreadsheets',
                    'https://www.googleapis.com/auth/drive.file'
                ]
            });
            
            return serviceAccountAuth;
        } catch (error) {
            console.error('Error creating service account auth:', error);
            throw new Error('Failed to create Google Sheets authentication');
        }
    },

    /**
     * Tạo Google Sheet mới cho dự án
     * @param {string} projectName - Tên dự án
     * @param {Array} headers - Mảng các header columns
     * @returns {Object} - {sheetId, sheetUrl}
     */
    createNewSheet: async (projectName, headers = []) => {
        try {
            // Kiểm tra credentials trước
            if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY) {
                console.warn('Google Sheets credentials not configured, skipping sheet creation');
                return {
                    sheetId: null,
                    sheetUrl: null
                };
            }

            const serviceAccountAuth = googleSheetsService.createServiceAccountAuth();

            // Sử dụng Google Sheets API trực tiếp
            const sheets = google.sheets({ version: 'v4', auth: serviceAccountAuth });

            // Tạo spreadsheet mới
            const createResponse = await sheets.spreadsheets.create({
                resource: {
                    properties: {
                        title: `Khảo sát - ${projectName} - ${new Date().toISOString().split('T')[0]}`
                    },
                    sheets: [{
                        properties: {
                            title: 'Dữ liệu khảo sát'
                        }
                    }]
                }
            });

            const spreadsheetId = createResponse.data.spreadsheetId;
            const spreadsheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit`;

            // Thêm headers nếu có
            if (headers && headers.length > 0) {
                await sheets.spreadsheets.values.update({
                    spreadsheetId,
                    range: 'A1',
                    valueInputOption: 'RAW',
                    resource: {
                        values: [headers]
                    }
                });
            }

            console.log(`✓ Google Sheet created successfully: ${spreadsheetUrl}`);

            return {
                sheetId: spreadsheetId,
                sheetUrl: spreadsheetUrl
            };
        } catch (error) {
            console.error('Error creating new sheet:', error);
            // Không throw error để không block việc tạo project
            console.warn('Continuing without Google Sheet creation');
            return {
                sheetId: null,
                sheetUrl: null
            };
        }
    },

    /**
     * Thêm dữ liệu vào Google Sheet
     * @param {string} sheetId - ID của Google Sheet
     * @param {Object} rowData - Dữ liệu dòng cần thêm
     * @param {Array} headers - Mảng headers để map dữ liệu
     * @returns {Object} - {success, rowIndex}
     */
    appendRowToSheet: async (sheetId, rowData, headers = []) => {
        try {
            // Kiểm tra credentials
            if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY) {
                console.warn('Google Sheets credentials not configured');
                return { success: false, error: 'Credentials not configured' };
            }

            if (!sheetId) {
                console.warn('No sheet ID provided');
                return { success: false, error: 'No sheet ID' };
            }

            const serviceAccountAuth = googleSheetsService.createServiceAccountAuth();
            const sheets = google.sheets({ version: 'v4', auth: serviceAccountAuth });

            // Chuẩn bị dữ liệu theo thứ tự headers
            const rowValues = [];
            headers.forEach(header => {
                rowValues.push(rowData[header] || '');
            });

            // Thêm timestamp
            rowValues.push(new Date().toLocaleString('vi-VN'));

            // Thêm dòng mới bằng append API
            const appendResponse = await sheets.spreadsheets.values.append({
                spreadsheetId: sheetId,
                range: 'A:A', // Append to first available row
                valueInputOption: 'RAW',
                insertDataOption: 'INSERT_ROWS',
                resource: {
                    values: [rowValues]
                }
            });

            console.log(`✓ Data appended to Google Sheet: ${sheetId}`);

            return {
                success: true,
                rowIndex: appendResponse.data.updates?.updatedRows || 1
            };
        } catch (error) {
            console.error('Error appending row to sheet:', error);
            return { success: false, error: error.message };
        }
    },

    /**
     * Cập nhật headers của Google Sheet dựa trên cấu hình khảo sát
     * @param {string} sheetId - ID của Google Sheet
     * @param {Array} surveyFields - Mảng các trường khảo sát
     * @returns {Array} - Mảng headers đã cập nhật
     */
    updateSheetHeaders: async (sheetId, surveyFields) => {
        try {
            // Kiểm tra credentials
            if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY) {
                console.warn('Google Sheets credentials not configured');
                return [];
            }

            if (!sheetId) {
                console.warn('No sheet ID provided for header update');
                return [];
            }

            const serviceAccountAuth = googleSheetsService.createServiceAccountAuth();
            const sheets = google.sheets({ version: 'v4', auth: serviceAccountAuth });

            // Tạo headers từ survey fields
            const headers = ['ID', 'Email người trả lời', 'IP Address'];

            surveyFields.forEach(field => {
                headers.push(field.field_label);
            });

            headers.push('Thời gian gửi');

            // Cập nhật headers bằng API
            await sheets.spreadsheets.values.update({
                spreadsheetId: sheetId,
                range: 'A1',
                valueInputOption: 'RAW',
                resource: {
                    values: [headers]
                }
            });

            console.log(`✓ Google Sheet headers updated for sheet: ${sheetId}`);

            return headers;
        } catch (error) {
            console.error('Error updating sheet headers:', error);
            return [];
        }
    },

    /**
     * Lấy thông tin Google Sheet
     * @param {string} sheetId - ID của Google Sheet
     * @returns {Object} - Thông tin sheet
     */
    getSheetInfo: async (sheetId) => {
        try {
            const serviceAccountAuth = googleSheetsService.createServiceAccountAuth();
            const sheets = google.sheets({ version: 'v4', auth: serviceAccountAuth });

            const response = await sheets.spreadsheets.get({
                spreadsheetId: sheetId
            });

            const spreadsheet = response.data;

            return {
                title: spreadsheet.properties.title,
                sheetCount: spreadsheet.sheets.length,
                url: `https://docs.google.com/spreadsheets/d/${sheetId}/edit`,
                lastUpdated: spreadsheet.properties.timeZone
            };
        } catch (error) {
            console.error('Error getting sheet info:', error);
            throw new Error('Failed to get Google Sheet information');
        }
    },

    /**
     * Kiểm tra quyền truy cập Google Sheet
     * @param {string} sheetId - ID của Google Sheet
     * @returns {boolean} - true nếu có quyền truy cập
     */
    checkSheetAccess: async (sheetId) => {
        try {
            await googleSheetsService.getSheetInfo(sheetId);
            return true;
        } catch (error) {
            return false;
        }
    },

    /**
     * Tạo dữ liệu dòng từ survey response
     * @param {Object} surveyResponse - Dữ liệu phản hồi khảo sát
     * @param {Array} surveyFields - Mảng các trường khảo sát
     * @param {Array} responseData - Dữ liệu chi tiết phản hồi
     * @returns {Object} - Dữ liệu dòng để ghi vào Google Sheet
     */
    prepareSurveyDataForSheet: (surveyResponse, surveyFields, responseData) => {
        const rowData = {
            'ID': surveyResponse.id,
            'Email người trả lời': surveyResponse.respondent_email || '',
            'IP Address': surveyResponse.respondent_ip || ''
        };
        
        // Map dữ liệu phản hồi theo từng field
        surveyFields.forEach(field => {
            const fieldResponse = responseData.find(data => data.survey_field_id === field.id);
            
            if (fieldResponse) {
                let value = fieldResponse.field_value;
                
                // Xử lý dữ liệu JSON cho multiselect, checkbox
                if (fieldResponse.field_value_json && 
                    ['multiselect', 'checkbox'].includes(field.field_type)) {
                    try {
                        const jsonValue = JSON.parse(fieldResponse.field_value_json);
                        value = Array.isArray(jsonValue) ? jsonValue.join(', ') : value;
                    } catch (e) {
                        // Giữ nguyên value nếu parse JSON thất bại
                    }
                }
                
                rowData[field.field_label] = value || '';
            } else {
                rowData[field.field_label] = '';
            }
        });
        
        return rowData;
    }
};

module.exports = googleSheetsService;
