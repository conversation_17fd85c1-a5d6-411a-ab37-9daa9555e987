const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');

class EmailService {
    constructor() {
        this.transporter = null;
        this.templates = {};
        this.init();
    }
    
    init() {
        // Configure email transporter
        this.transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST || 'localhost',
            port: process.env.SMTP_PORT || 587,
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });
        
        // Load email templates
        this.loadTemplates();
    }
    
    loadTemplates() {
        const templatesDir = path.join(__dirname, '../email-templates');
        
        if (!fs.existsSync(templatesDir)) {
            fs.mkdirSync(templatesDir, { recursive: true });
        }
        
        const templateFiles = [
            'survey-invitation.hbs',
            'survey-reminder.hbs',
            'survey-completion.hbs',
            'survey-report.hbs',
            'admin-notification.hbs'
        ];
        
        templateFiles.forEach(filename => {
            const templatePath = path.join(templatesDir, filename);
            if (fs.existsSync(templatePath)) {
                const templateContent = fs.readFileSync(templatePath, 'utf8');
                const templateName = path.basename(filename, '.hbs');
                this.templates[templateName] = handlebars.compile(templateContent);
            }
        });
    }
    
    /**
     * Send survey invitation email
     */
    async sendSurveyInvitation(options) {
        const {
            to,
            surveyName,
            surveyUrl,
            senderName,
            customMessage,
            expiryDate
        } = options;
        
        const templateData = {
            surveyName,
            surveyUrl,
            senderName,
            customMessage,
            expiryDate: expiryDate ? new Date(expiryDate).toLocaleDateString() : null,
            currentYear: new Date().getFullYear()
        };
        
        const htmlContent = this.templates['survey-invitation'] 
            ? this.templates['survey-invitation'](templateData)
            : this.getDefaultInvitationTemplate(templateData);
        
        const mailOptions = {
            from: process.env.SMTP_FROM || '<EMAIL>',
            to: to,
            subject: `Invitation: ${surveyName}`,
            html: htmlContent,
            text: this.htmlToText(htmlContent)
        };
        
        return await this.sendEmail(mailOptions);
    }
    
    /**
     * Send survey reminder email
     */
    async sendSurveyReminder(options) {
        const {
            to,
            surveyName,
            surveyUrl,
            reminderNumber,
            expiryDate
        } = options;
        
        const templateData = {
            surveyName,
            surveyUrl,
            reminderNumber,
            expiryDate: expiryDate ? new Date(expiryDate).toLocaleDateString() : null,
            currentYear: new Date().getFullYear()
        };
        
        const htmlContent = this.templates['survey-reminder']
            ? this.templates['survey-reminder'](templateData)
            : this.getDefaultReminderTemplate(templateData);
        
        const mailOptions = {
            from: process.env.SMTP_FROM || '<EMAIL>',
            to: to,
            subject: `Reminder: ${surveyName}`,
            html: htmlContent,
            text: this.htmlToText(htmlContent)
        };
        
        return await this.sendEmail(mailOptions);
    }
    
    /**
     * Send survey completion notification
     */
    async sendSurveyCompletion(options) {
        const {
            to,
            surveyName,
            respondentEmail,
            submissionTime,
            responseData
        } = options;
        
        const templateData = {
            surveyName,
            respondentEmail,
            submissionTime: new Date(submissionTime).toLocaleString(),
            responseData,
            currentYear: new Date().getFullYear()
        };
        
        const htmlContent = this.templates['survey-completion']
            ? this.templates['survey-completion'](templateData)
            : this.getDefaultCompletionTemplate(templateData);
        
        const mailOptions = {
            from: process.env.SMTP_FROM || '<EMAIL>',
            to: to,
            subject: `New Response: ${surveyName}`,
            html: htmlContent,
            text: this.htmlToText(htmlContent)
        };
        
        return await this.sendEmail(mailOptions);
    }
    
    /**
     * Send survey report email
     */
    async sendSurveyReport(options) {
        const {
            to,
            surveyName,
            reportData,
            attachments
        } = options;
        
        const templateData = {
            surveyName,
            reportData,
            currentYear: new Date().getFullYear()
        };
        
        const htmlContent = this.templates['survey-report']
            ? this.templates['survey-report'](templateData)
            : this.getDefaultReportTemplate(templateData);
        
        const mailOptions = {
            from: process.env.SMTP_FROM || '<EMAIL>',
            to: to,
            subject: `Survey Report: ${surveyName}`,
            html: htmlContent,
            text: this.htmlToText(htmlContent),
            attachments: attachments || []
        };
        
        return await this.sendEmail(mailOptions);
    }
    
    /**
     * Send admin notification
     */
    async sendAdminNotification(options) {
        const {
            to,
            subject,
            message,
            priority = 'normal',
            data
        } = options;
        
        const templateData = {
            subject,
            message,
            priority,
            data,
            timestamp: new Date().toLocaleString(),
            currentYear: new Date().getFullYear()
        };
        
        const htmlContent = this.templates['admin-notification']
            ? this.templates['admin-notification'](templateData)
            : this.getDefaultAdminTemplate(templateData);
        
        const mailOptions = {
            from: process.env.SMTP_FROM || '<EMAIL>',
            to: to,
            subject: `[${priority.toUpperCase()}] ${subject}`,
            html: htmlContent,
            text: this.htmlToText(htmlContent)
        };
        
        return await this.sendEmail(mailOptions);
    }
    
    /**
     * Send email with retry logic
     */
    async sendEmail(mailOptions, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                const info = await this.transporter.sendMail(mailOptions);
                console.log('Email sent successfully:', info.messageId);
                return {
                    success: true,
                    messageId: info.messageId,
                    response: info.response
                };
            } catch (error) {
                console.error(`Email send attempt ${i + 1} failed:`, error.message);
                
                if (i === retries - 1) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
            }
        }
    }
    
    /**
     * Verify email configuration
     */
    async verifyConnection() {
        try {
            await this.transporter.verify();
            return { success: true, message: 'Email configuration is valid' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Default email templates
     */
    getDefaultInvitationTemplate(data) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Survey Invitation</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
                    .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Survey Invitation</h1>
                    </div>
                    <div class="content">
                        <h2>${data.surveyName}</h2>
                        <p>Hello,</p>
                        <p>You have been invited to participate in our survey: <strong>${data.surveyName}</strong></p>
                        ${data.customMessage ? `<p>${data.customMessage}</p>` : ''}
                        <p>Your participation is valuable to us and will help improve our services.</p>
                        ${data.expiryDate ? `<p><strong>Please complete by:</strong> ${data.expiryDate}</p>` : ''}
                        <p style="text-align: center; margin: 30px 0;">
                            <a href="${data.surveyUrl}" class="button">Take Survey</a>
                        </p>
                        <p>If the button doesn't work, copy and paste this link into your browser:</p>
                        <p><a href="${data.surveyUrl}">${data.surveyUrl}</a></p>
                        <p>Thank you for your time!</p>
                        ${data.senderName ? `<p>Best regards,<br>${data.senderName}</p>` : ''}
                    </div>
                    <div class="footer">
                        <p>&copy; ${data.currentYear} Survey System. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
    
    getDefaultReminderTemplate(data) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Survey Reminder</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #ffc107; color: #333; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .button { display: inline-block; padding: 12px 24px; background: #ffc107; color: #333; text-decoration: none; border-radius: 4px; }
                    .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Survey Reminder</h1>
                    </div>
                    <div class="content">
                        <h2>${data.surveyName}</h2>
                        <p>Hello,</p>
                        <p>This is a friendly reminder that you have a pending survey to complete: <strong>${data.surveyName}</strong></p>
                        <p>We haven't received your response yet, and your input is important to us.</p>
                        ${data.expiryDate ? `<p><strong>Please complete by:</strong> ${data.expiryDate}</p>` : ''}
                        <p style="text-align: center; margin: 30px 0;">
                            <a href="${data.surveyUrl}" class="button">Complete Survey Now</a>
                        </p>
                        <p>If the button doesn't work, copy and paste this link into your browser:</p>
                        <p><a href="${data.surveyUrl}">${data.surveyUrl}</a></p>
                        <p>Thank you for your time!</p>
                    </div>
                    <div class="footer">
                        <p>&copy; ${data.currentYear} Survey System. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
    
    getDefaultCompletionTemplate(data) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>New Survey Response</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #28a745; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>New Survey Response</h1>
                    </div>
                    <div class="content">
                        <h2>${data.surveyName}</h2>
                        <p>A new response has been submitted for your survey.</p>
                        <p><strong>Respondent:</strong> ${data.respondentEmail || 'Anonymous'}</p>
                        <p><strong>Submitted:</strong> ${data.submissionTime}</p>
                        <p>You can view the complete response in your survey dashboard.</p>
                    </div>
                    <div class="footer">
                        <p>&copy; ${data.currentYear} Survey System. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
    
    getDefaultReportTemplate(data) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Survey Report</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #6f42c1; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Survey Report</h1>
                    </div>
                    <div class="content">
                        <h2>${data.surveyName}</h2>
                        <p>Please find attached the survey report for ${data.surveyName}.</p>
                        <p>This report contains detailed analytics and insights from the survey responses.</p>
                    </div>
                    <div class="footer">
                        <p>&copy; ${data.currentYear} Survey System. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
    
    getDefaultAdminTemplate(data) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Admin Notification</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Admin Notification</h1>
                    </div>
                    <div class="content">
                        <h2>${data.subject}</h2>
                        <p><strong>Priority:</strong> ${data.priority.toUpperCase()}</p>
                        <p><strong>Time:</strong> ${data.timestamp}</p>
                        <p>${data.message}</p>
                        ${data.data ? `<pre>${JSON.stringify(data.data, null, 2)}</pre>` : ''}
                    </div>
                    <div class="footer">
                        <p>&copy; ${data.currentYear} Survey System. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
    
    /**
     * Convert HTML to plain text
     */
    htmlToText(html) {
        return html
            .replace(/<[^>]*>/g, '')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/\s+/g, ' ')
            .trim();
    }
}

module.exports = new EmailService();
