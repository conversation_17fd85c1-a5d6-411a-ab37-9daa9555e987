# Hướng dẫn Debug Admin Menu - Thêm thực phẩm và món ăn

## Lỗi đã khắc phục: `Cannot read properties of undefined (reading 'customData')`

### Nguyên nhân gốc rễ:
1. **Virtual Select `getSelectedOptions()`** trả về array, nhưng code cũ truy cập trực tiếp `selectedFoodOptions.customData` thay vì `selectedFoodOptions[0].customData`
2. **Không kiểm tra existence** của `selectedFoodOptions[0]` trước khi truy cập properties
3. **Không validation** dữ liệu customData có tồn tại hay không

### Giải pháp đã áp dụng:

#### 1. Sửa hàm `addFoodToMenuAdmin()` (cho trang admin):
```javascript
// TRƯỚC (LỖI):
const selectedFoodOptions = foodSelect.getSelectedOptions();
const foodData = selectedFoodOptions[0].customData; // ❌ Lỗi ở đây

// SAU (ĐÚNG):
const selectedFoodOptions = foodSelect.getSelectedOptions();

// Kiểm tra array không rỗng
if (!selectedFoodOptions || selectedFoodOptions.length === 0) {
    toarstError('Vui lòng chọn thực phẩm!');
    return;
}

// Kiểm tra element đầu tiên
const selectedOption = selectedFoodOptions[0];
if (!selectedOption) {
    toarstError('Dữ liệu thực phẩm được chọn không hợp lệ!');
    return;
}

// Kiểm tra customData
if (!selectedOption.customData) {
    console.error('Missing customData in selected option:', selectedOption);
    toarstError('Thông tin dinh dưỡng của thực phẩm không có sẵn!');
    return;
}

const foodData = selectedOption.customData; // ✅ An toàn
```

#### 2. Sửa hàm `addFoodToMenu()` (cho trang user):
```javascript
// Áp dụng cùng logic validation để đảm bảo tương thích
const selectedOption = selectedFoodOptions[0];
if (!selectedOption || !selectedOption.customData) {
    toarstError('Thông tin dinh dưỡng của thực phẩm không có sẵn!');
    return;
}
```

## Cách test và debug:

### 1. **Kiểm tra Console Logs**

Mở Developer Tools (F12) và xem console khi:

#### Trang Admin (`/admin/thuc-don-mau`):
```javascript
// Logs cần thấy:
✅ "Admin addFoodToMenu elements check: {menuTimeSelect: true, foodSelect: true, menuExamine: [...]}"
✅ "Selected food options: [{value: 123, label: 'Gạo tẻ', customData: {...}}]"
✅ "Food data and weight: {foodData: {...}, weight: 100}"
✅ "Food added successfully"

// Logs báo lỗi (nếu có):
❌ "Missing customData in selected option: {...}"
❌ "Dữ liệu thực phẩm được chọn không hợp lệ!"
```

#### Trang User (`/khau-phan-an`):
```javascript
// Logs cần thấy:
✅ "Using addFoodToMenu for user page..."
✅ "foodSelect [{value: 123, label: 'Gạo tẻ', customData: {...}}]"
✅ "addFoodToMenu 1 [{id: 1, name: 'Menu 1', detail: [...]}]"
```

### 2. **Kiểm tra API Response**

#### Test API Food Search:
```javascript
// Mở console và chạy:
fetch('/api/food-search?search=gạo')
  .then(r => r.json())
  .then(data => console.log('API Response:', data));

// Kết quả mong đợi:
{
  "success": true,
  "data": [
    {
      "value": 123,
      "label": "Gạo tẻ - Rice (Chín - 2024)",
      "customData": {
        "id": 123,
        "energy": 130,
        "protein": 2.7,
        "fat": 0.3,
        // ... các trường khác
      }
    }
  ]
}
```

#### Test API Dishes:
```javascript
// Chỉ có trang admin:
fetch('/admin/api/dishes-for-select')
  .then(r => r.json())
  .then(data => console.log('Dishes API:', data));
```

### 3. **Kiểm tra Virtual Select Initialization**

#### Trong trang Admin:
```javascript
// Kiểm tra elements tồn tại:
const foodSelect = document.querySelector('#food_name');
const dishSelect = document.querySelector('#dish_name');

console.log('Food Select Instance:', foodSelect?.virtualSelect);
console.log('Dish Select Instance:', dishSelect?.virtualSelect);

// Cả 2 phải trả về object, không phải undefined
```

#### Trong trang User:
```javascript
// Chỉ có food select:
const foodSelect = document.querySelector('#food_name');
console.log('Food Select Instance:', foodSelect?.virtualSelect);
```

### 4. **Test Cases cụ thể**

#### Test Case 1: Thêm thực phẩm (Admin)
```
1. Truy cập: /admin/thuc-don-mau/new
2. Chọn "Bữa sáng" từ dropdown đầu tiên
3. Search "gạo" → chọn "Gạo tẻ"
4. Nhập khối lượng: 100
5. Click "Thêm"
6. ✅ Thấy thông báo: "Đã thêm thực phẩm vào thực đơn!"
7. ✅ Thấy "Gạo tẻ" xuất hiện trong bảng
```

#### Test Case 2: Thêm món ăn (Admin)
```
1. Chọn "Bữa trưa" từ dropdown thứ hai  
2. Chọn món ăn từ dropdown
3. Click "Thêm món ăn"
4. ✅ Thấy thông báo: "Đã thêm món ... vào thực đơn!"
5. ✅ Thấy tất cả thực phẩm trong món xuất hiện
```

#### Test Case 3: Filter thực phẩm
```
1. Đổi "Loại thực phẩm" → "Sống"
2. Đổi "Năm dữ liệu" → "2024"  
3. Search lại
4. ✅ Kết quả chỉ hiển thị thực phẩm sống, năm 2024
```

### 5. **Common Errors và Solutions**

#### Lỗi: "Không tìm thấy form elements!"
**Nguyên nhân:** Virtual Select chưa được khởi tạo
**Giải pháp:** 
```javascript
// Đảm bảo gọi đúng thứ tự:
$(document).ready(function() {
    initAdminMenuExample(); // Sẽ gọi generateFoodName() và generateDishName()
    generateFoodName("food_name");
    // Tạo column selector
    createColumnSelector();
});
```

#### Lỗi: "Thông tin dinh dưỡng của thực phẩm không có sẵn!"
**Nguyên nhân:** API `/api/food-search` không trả về customData đúng
**Giải pháp:** Kiểm tra API response có đúng format không

#### Lỗi: "Không tìm thấy giờ ăn được chọn!"
**Nguyên nhân:** `menuExamine[0].detail` không có menuTime với ID tương ứng
**Giải pháp:** 
```javascript
console.log('Available menu times:', window.menuExamine[0].detail);
console.log('Selected menu time ID:', menuTime_id);
```

### 6. **Performance Monitoring**

#### Kiểm tra thời gian API:
```javascript
console.time('FoodSearch');
fetch('/api/food-search?search=gạo')
  .then(r => r.json())
  .then(data => {
    console.timeEnd('FoodSearch'); // Nên < 1000ms
    console.log('Results count:', data.data?.length);
  });
```

#### Kiểm tra Memory Usage:
```javascript
// Sau khi thêm nhiều thực phẩm:
console.log('MenuExamine size:', JSON.stringify(window.menuExamine).length);
console.log('Total foods:', window.menuExamine[0].detail.reduce((sum, mt) => sum + mt.listFood.length, 0));
```

## Tóm tắt các điểm cần lưu ý:

1. **Luôn kiểm tra array length** trước khi truy cập `[0]`
2. **Validate customData** trước khi sử dụng
3. **Admin và User pages** sử dụng chung file JS nhưng có logic riêng
4. **API calls** cần có proper error handling
5. **Virtual Select** cần thời gian để initialize
6. **Console logs** là công cụ debug chính

## Backup Plan:

Nếu vẫn có lỗi, có thể fallback về cách cũ bằng cách:
```javascript
// Trong emergency, có thể tạm thời comment out validation:
// if (!selectedOption.customData) { return; }

// Và thêm try-catch:
try {
    const foodData = selectedOption?.customData || {};
    // ... rest of code
} catch (error) {
    console.error('Fallback error:', error);
    toarstError('Vui lòng thử lại!');
}
``` 