# Hướng dẫn Sử dụng Form Thêm/Sửa Món ăn

## Tổng quan
Form thêm/sửa món ăn cho phép quản trị viên tạo và quản lý các món ăn bao gồm nhiều thực phẩm khác nhau với khối lượng cụ thể. Hệ thống sẽ tự động tính toán tổng dinh dưỡng dựa trên thực phẩm và khối lượng đã chọn.

## Cách truy cập

### Tạo món ăn mới
1. Vào **Admin Panel** → **Quản lý món ăn**
2. Click nút **"Thêm món ăn mới"**
3. URL: `/admin/mon-an/new`

### Chỉnh sửa món ăn
1. Vào **Admin Panel** → **Quản lý món ăn**
2. Click biểu tượng **mắt** ở món ăn cần sửa
3. URL: `/admin/mon-an/{id}`

## Cách sử dụng Form

### 1. Thông tin cơ bản món ăn

#### Tên món ăn (Bắt buộc)
- Nhập tên món ăn rõ ràng, dễ hiểu
- Ví dụ: "Cơm gà Hải Nam", "Phở bò tái"

#### Loại món ăn
Chọn từ danh sách:
- **Món chính**: Cơm, phở, bún, mì...
- **Món phụ**: Gỏi, salad, đồ chua...
- **Canh**: Canh chua, canh rau...
- **Tráng miệng**: Chè, bánh, hoa quả...
- **Nước uống**: Nước ép, sinh tố...
- **Khác**: Các món không thuộc loại trên

#### Mô tả món ăn
- Mô tả ngắn gọn về món ăn
- Cách chế biến, nguyên liệu chính
- Đặc điểm dinh dưỡng nổi bật

### 2. Thêm thực phẩm vào món ăn

#### Bước 1: Lọc thực phẩm
- **Loại thực phẩm**: Chọn "Sống" hoặc "Chín" để thu hẹp kết quả
- **Năm dữ liệu**: Chọn "2017" hoặc "2024" theo bộ dữ liệu

#### Bước 2: Tìm kiếm thực phẩm
- Nhập tên thực phẩm vào ô **"Chọn thực phẩm"**
- Tối thiểu 2 ký tự để bắt đầu tìm kiếm
- Hệ thống sẽ hiển thị danh sách gợi ý
- Chọn thực phẩm phù hợp từ danh sách

#### Bước 3: Nhập khối lượng
- Nhập khối lượng thực phẩm tính bằng **gram**
- Ví dụ: 100, 150.5, 200
- Chấp nhận số thập phân (dùng dấu chấm)

#### Bước 4: Thêm vào danh sách
- Click nút **"Thêm thực phẩm"**
- Thực phẩm sẽ xuất hiện trong bảng bên dưới
- Hệ thống tự động tính dinh dưỡng theo khối lượng

### 3. Quản lý danh sách thực phẩm

#### Xem thông tin
Bảng hiển thị các thông tin:
- **STT**: Thứ tự thực phẩm
- **Tên thực phẩm**: Tên đầy đủ
- **Mã**: Mã thực phẩm (nếu có)
- **Loại**: Sống/Chín và năm dữ liệu
- **Khối lượng**: Khối lượng tính bằng gram
- **Năng lượng**: Kcal tính theo khối lượng thực tế
- **Protein**: Gram protein
- **Lipid**: Gram chất béo

#### Tổng kết dinh dưỡng
Phía trên bảng hiển thị:
- **Tổng khối lượng**: Tổng gram của tất cả thực phẩm
- **Tổng năng lượng**: Tổng kcal của món ăn

#### Xóa thực phẩm
- Click nút **thùng rác đỏ** ở cột "Thao tác"
- Thực phẩm sẽ bị xóa khỏi danh sách
- Tổng dinh dưỡng tự động cập nhật

### 4. Lưu món ăn

#### Kiểm tra trước khi lưu
- **Tên món ăn**: Phải có tên
- **Danh sách thực phẩm**: Phải có ít nhất 1 thực phẩm

#### Lưu dữ liệu
- Click nút **"Lưu món ăn"** (tạo mới) hoặc **"Cập nhật món ăn"** (chỉnh sửa)
- Hệ thống sẽ hiển thị thông báo thành công
- Tự động chuyển về danh sách món ăn sau 1.5 giây

#### Hủy thao tác
- Click nút **"Hủy"** để quay lại danh sách
- Dữ liệu chưa lưu sẽ bị mất

## Tính năng đặc biệt

### 1. Tìm kiếm thông minh
- Tìm kiếm theo tên thực phẩm
- Tìm kiếm theo mã thực phẩm
- Hỗ trợ tiếng Việt có dấu
- Kết quả giới hạn 100 để tối ưu hiệu suất

### 2. Lọc thông minh
- Lọc theo loại thực phẩm (sống/chín)
- Lọc theo năm dữ liệu (2017/2024)
- Kết hợp nhiều bộ lọc

### 3. Tính toán dinh dưỡng tự động
- Dữ liệu gốc lưu theo 100g
- Tự động quy đổi theo khối lượng thực tế
- Công thức: `(giá_trị_100g / 100) * khối_lượng_thực_tế`

### 4. Kiểm tra trùng lặp
- Không cho phép thêm cùng 1 thực phẩm 2 lần
- Hiển thị cảnh báo nếu trùng lặp

### 5. Responsive Design
- Giao diện thân thiện trên mobile
- Tự động điều chỉnh layout theo màn hình

## Lưu ý quan trọng

### Dữ liệu đầu vào
- **Khối lượng**: Phải là số dương
- **Tên món ăn**: Không được để trống
- **Thực phẩm**: Phải chọn từ danh sách có sẵn

### Hiệu suất
- Tìm kiếm có độ trễ 500ms để tránh quá tải server
- Giới hạn 100 kết quả mỗi lần tìm kiếm
- Sử dụng cache để tối ưu tốc độ

### Bảo mật
- Chỉ admin mới có quyền truy cập
- Validate dữ liệu ở cả client và server
- Xử lý lỗi graceful

## Xử lý lỗi thường gặp

### 1. "Vui lòng chọn thực phẩm!"
- **Nguyên nhân**: Chưa chọn thực phẩm từ danh sách
- **Giải pháp**: Tìm kiếm và chọn thực phẩm trước khi thêm

### 2. "Vui lòng nhập khối lượng hợp lệ!"
- **Nguyên nhân**: Khối lượng <= 0 hoặc không phải số
- **Giải pháp**: Nhập số dương, ví dụ: 100, 150.5

### 3. "Thực phẩm đã có trong danh sách!"
- **Nguyên nhân**: Thực phẩm đã được thêm trước đó
- **Giải pháp**: Xóa thực phẩm cũ hoặc chọn thực phẩm khác

### 4. "Vui lòng nhập tên món ăn!"
- **Nguyên nhân**: Tên món ăn để trống
- **Giải pháp**: Nhập tên món ăn trước khi lưu

### 5. "Vui lòng thêm ít nhất một thực phẩm vào món ăn!"
- **Nguyên nhân**: Danh sách thực phẩm trống
- **Giải pháp**: Thêm ít nhất 1 thực phẩm trước khi lưu

## API liên quan

### GET `/admin/mon-an/new`
Hiển thị form tạo món ăn mới

### GET `/admin/mon-an/{id}`
Hiển thị form chỉnh sửa món ăn

### POST `/admin/mon-an/upsert`
Lưu/cập nhật món ăn
- **Tham số**: form data + JSON danh sách thực phẩm
- **Response**: JSON với status và message

### GET `/api/food-search`
API tìm kiếm thực phẩm
- **Tham số**: search, type, type_year
- **Response**: JSON danh sách thực phẩm

## Cấu trúc dữ liệu

### Món ăn (dishes)
```sql
{
  id: int,
  name: string,
  description: text,
  category: string,
  created_by: int,
  created_at: timestamp,
  active: tinyint
}
```

### Thực phẩm trong món ăn (dish_foods)
```sql
{
  id: int,
  dish_id: int,
  food_id: int,
  weight: decimal(10,2),
  order_index: int
}
```

### JSON gửi lên server
```javascript
{
  name: "Tên món ăn",
  description: "Mô tả",
  category: "chính",
  dish_foods: [
    {
      food_id: 123,
      weight: 100.5
    }
  ]
}
```

## Kết luận
Form thêm/sửa món ăn cung cấp giao diện trực quan và mạnh mẽ để quản lý món ăn với tính toán dinh dưỡng tự động. Sử dụng công nghệ Virtual Select cho trải nghiệm tìm kiếm mượt mà và responsive design cho tương thích đa thiết bị. 