# Hướng dẫn tạo detail mặc định cho thực đơn mẫu

## Tổng quan
Khi tạo mới thực đơn mẫu thông qua hàm `addDatatable('menu_example')`, hệ thống sẽ tự động tạo trường `detail` với cấu trúc mặc định chứa tất cả các `menu_time` có sẵn.

## Cấu trúc detail mặc định
```json
{
    "id": 7,
    "name": "Thực đơn 7", 
    "detail": [
        {
            "id": 3,
            "name": "6h - 6h30",
            "name_course": "",
            "listFood": []
        },
        {
            "id": 4,
            "name": "9h",
            "name_course": "",
            "listFood": []
        },
        {
            "id": 5,
            "name": "11h30 - 12h",
            "name_course": "",
            "listFood": []
        },
        {
            "id": 6,
            "name": "15h",
            "name_course": "",
            "listFood": []
        },
        {
            "id": 7,
            "name": "17h30 - 18h", 
            "name_course": "",
            "listFood": []
        },
        {
            "id": 8,
            "name": "21h",
            "name_course": "",
            "listFood": []
        }
    ],
    "note": "",
    "created_at": "2025-06-29T14:50:48.034Z"
}
```

## Các thay đổi đã thực hiện

### 1. Controller - adminController.js
- **Hàm**: `menuExampleList`
- **Thay đổi**: Thêm việc lấy dữ liệu `menuTime` từ database và chuyển xuống view
- **Mục đích**: Cung cấp danh sách menu_time cho JavaScript frontend

```javascript
menuExampleList: async (req, res) => {
    let errors = [];
    let menuTime = [];
    
    try {
        // Lấy danh sách thời gian ăn
        const menuTimeRes = await commonService.getAllDataTable('menu_time', {});
        if (menuTimeRes.success && menuTimeRes.data) {
            menuTime = menuTimeRes.data.map(item => ({
                id: item.id,
                name: item.time
            }));
        }
    } catch (error) {
        console.error('Error loading menuTime:', error);
    }
    
    res.render('admin/thuc-don-mau/list', {
        user: req.user,
        errors: errors,
        menuTime: menuTime
    });
},
```

### 2. View - list.ejs
- **File**: `views/admin/thuc-don-mau/list.ejs`
- **Thay đổi**: Thêm biến global `window.listMenuTime`
- **Mục đích**: Cho phép JavaScript truy cập dữ liệu menuTime

```html
<script>
    window.listMenuTime = <%-JSON.stringify(menuTime)%>;
</script>
```

### 3. JavaScript - admin.js
- **Hàm**: `getDataMenuExample()`
- **Thay đổi**: Thêm logic tạo detail mặc định với cấu trúc menu_time
- **Mục đích**: Tự động tạo detail khi tạo mới thực đơn mẫu

```javascript
function getDataMenuExample(){
    // Tạo detail mặc định với menuTime
    let defaultDetail = [];
    if (window.listMenuTime && window.listMenuTime.length > 0) {
        defaultDetail = window.listMenuTime.map(time => ({
            id: time.id,
            name: time.name,
            name_course: "",
            listFood: []
        }));
    }
    
    return {
        name_menu: $('#name_menu_modal').val(),
        share: $('#share').val(),
        detail: JSON.stringify(defaultDetail),
        url: '/admin/thuc-don-mau/upsert/'
    }
}
```

## Luồng hoạt động

1. **Khi mở trang danh sách thực đơn mẫu** (`/admin/thuc-don-mau`):
   - Controller `menuExampleList` lấy dữ liệu `menu_time` từ database
   - Chuyển dữ liệu xuống view `list.ejs`
   - View tạo biến global `window.listMenuTime`

2. **Khi nhấn nút "Thêm mới" và điền form**:
   - Modal hiển thị form nhập tên và trạng thái chia sẻ
   - Khi nhấn "Lưu", hàm `addDataTable('menu_example')` được gọi

3. **Trong quá trình xử lý**:
   - `addDataTable()` gọi `getDataMenuExample()`
   - `getDataMenuExample()` tạo detail mặc định từ `window.listMenuTime`
   - Gửi request POST đến `/admin/thuc-don-mau/upsert/`

4. **Controller xử lý**:
   - `menuExampleUpsert` nhận dữ liệu bao gồm `detail` đã được tạo sẵn
   - Lưu vào database với cấu trúc detail hoàn chỉnh

## Lợi ích

1. **Tiết kiệm thời gian**: Không cần tạo từng menu_time thủ công
2. **Nhất quán**: Tất cả thực đơn mới đều có cấu trúc giống nhau
3. **Tránh lỗi**: Không bị thiếu menu_time nào
4. **Sẵn sàng sử dụng**: Có thể thêm thực phẩm ngay sau khi tạo

## Test và kiểm tra

- File test: `test_menu_detail_creation.html`
- Chạy test để kiểm tra logic tạo detail
- Kiểm tra trên browser tại `/admin/thuc-don-mau`

## Tương thích

- Hoạt động với tất cả menu_time hiện có trong database
- Không ảnh hưởng đến thực đơn mẫu đã tồn tại
- Tương thích với chức năng chỉnh sửa và xóa 