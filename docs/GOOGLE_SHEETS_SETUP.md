# 📊 Google Sheets Integration Setup

## ✅ Trạng thái hiện tại

Google Sheets service đã được **sửa hoàn toàn** và hoạt động ổn định:

- ✅ **Không còn lỗi API** - Sử dụng `googleapis` trực tiếp thay vì `google-spreadsheet`
- ✅ **Error handling tốt** - Không crash khi thiếu credentials
- ✅ **Graceful degradation** - Hệ thống vẫn hoạt động mà không cần Google Sheets
- ✅ **Logging rõ ràng** - Thông báo khi thành công/thất bại

## 🚀 Hệ thống hoạt động ngay

**Không cần Google Sheets credentials**, hệ thống vẫn hoạt động đầy đủ:

1. ✅ Tạo dự án thành công
2. ✅ Tạo khảo sát thành công  
3. ✅ Cấu hình trường thành công
4. ✅ Submit form thành công
5. ✅ Dữ liệu lưu trong database

## 📋 Để bật Google Sheets (<PERSON><PERSON><PERSON> chọn)

### Bước 1: Tạo Google Cloud Project

1. <PERSON><PERSON><PERSON> cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Enable APIs:
   - Google Sheets API
   - Google Drive API

### Bước 2: Tạo Service Account

1. Vào **IAM & Admin** > **Service Accounts**
2. Click **Create Service Account**
3. Điền thông tin:
   - **Name**: Survey System Service Account
   - **Description**: For survey system Google Sheets integration
4. Click **Create and Continue**
5. **Grant roles** (tùy chọn):
   - Editor (hoặc chỉ cần Google Sheets permissions)
6. Click **Done**

### Bước 3: Tạo Key

1. Click vào Service Account vừa tạo
2. Vào tab **Keys**
3. Click **Add Key** > **Create new key**
4. Chọn **JSON** format
5. Click **Create** - File JSON sẽ được download

### Bước 4: Cấu hình Environment Variables

Mở file `.env` và thêm:

```env
# Google Sheets Integration (Optional)
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
```

**Lưu ý quan trọng:**
- Copy email từ file JSON: `client_email`
- Copy private key từ file JSON: `private_key` 
- **Giữ nguyên** `\n` trong private key
- **Bọc trong dấu ngoặc kép**

### Bước 5: Test Integration

```bash
# Test Google Sheets service
node test/simple-sheets-test.js

# Nếu thành công sẽ thấy:
# ✅ Sheet created successfully!
# Sheet ID: 1ABC...XYZ
# Sheet URL: https://docs.google.com/spreadsheets/d/1ABC...XYZ/edit
```

## 🔧 Troubleshooting

### Lỗi: "Error creating new sheet"

**Nguyên nhân phổ biến:**

1. **Invalid credentials format**
   ```
   ❌ GOOGLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----...
   ✅ GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
   ```

2. **Service Account không có quyền**
   - Đảm bảo Service Account có role Editor
   - Hoặc enable Google Sheets API permissions

3. **API chưa được enable**
   - Enable Google Sheets API
   - Enable Google Drive API

### Lỗi: "Permission denied"

**Giải pháp:**
1. Share Google Drive folder với Service Account email
2. Hoặc tạo sheet trong Drive của Service Account

### Test không thành công

```bash
# Kiểm tra credentials
node -e "console.log('Email:', process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL); console.log('Key length:', process.env.GOOGLE_PRIVATE_KEY?.length);"

# Nếu undefined -> Kiểm tra file .env
# Nếu có giá trị -> Kiểm tra format
```

## 📊 Cách hoạt động

### Khi có credentials:
1. **Tạo dự án** → Tự động tạo Google Sheet
2. **Cấu hình trường** → Tự động cập nhật headers
3. **Submit form** → Tự động lưu vào sheet

### Khi không có credentials:
1. **Tạo dự án** → Chỉ lưu database (vẫn thành công)
2. **Cấu hình trường** → Chỉ lưu database
3. **Submit form** → Chỉ lưu database

## 🎯 Lợi ích của Google Sheets

- **📊 Real-time data** - Xem dữ liệu ngay lập tức
- **📈 Easy analysis** - Sử dụng Google Sheets functions
- **👥 Collaboration** - Share với team members
- **📱 Mobile access** - Xem trên điện thoại
- **🔄 Auto backup** - Dữ liệu được backup tự động

## 🔒 Bảo mật

### Service Account Key:
- **Không commit** vào Git
- **Không share** publicly
- **Rotate key** định kỳ
- **Limit permissions** chỉ cần thiết

### Best Practices:
```env
# ✅ Đúng - Trong .env file
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# ❌ Sai - Hardcode trong code
const privateKey = "-----BEGIN PRIVATE KEY-----...";
```

## 📝 Example .env

```env
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=password
DB_NAME=patients

# Google Sheets (Optional)
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"

# Other configs...
```

## ✅ Verification Checklist

- [ ] Google Cloud Project created
- [ ] Google Sheets API enabled
- [ ] Google Drive API enabled  
- [ ] Service Account created
- [ ] JSON key downloaded
- [ ] Environment variables configured
- [ ] Test passed: `node test/simple-sheets-test.js`
- [ ] Project creation works with Google Sheet
- [ ] Survey submission saves to Google Sheet

---

**Kết luận:** Google Sheets integration đã hoạt động hoàn hảo. Hệ thống có thể chạy với hoặc không có Google Sheets credentials!
