/* Survey System Styles */

/* Project Management Styles */
.project-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.project-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.project-header {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    padding: 1rem;
    border-radius: 0.35rem 0.35rem 0 0;
}

.project-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.project-status.active {
    background-color: #1cc88a;
    color: white;
}

.project-status.inactive {
    background-color: #f6c23e;
    color: white;
}

.project-status.deleted {
    background-color: #e74a3b;
    color: white;
}

/* Survey Config Styles */
.survey-config-card {
    border-left: 4px solid #4e73df;
    background: #f8f9fc;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.35rem;
}

.survey-url-display {
    background: #e2e6ea;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.9rem;
    word-break: break-all;
}

.copy-link-btn {
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-link-btn:hover {
    transform: scale(1.1);
}

/* Field Configuration Styles */
.field-config-container {
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.field-item {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.field-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.field-drag-handle {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    cursor: move;
    color: #858796;
    font-size: 1.2rem;
}

.field-drag-handle:hover {
    color: #4e73df;
}

.field-type-selector {
    margin-bottom: 1rem;
}

.field-type-option {
    display: inline-block;
    margin: 0.25rem;
    padding: 0.5rem 1rem;
    background: #e3e6f0;
    border: 1px solid #d1d3e2;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.field-type-option:hover {
    background: #4e73df;
    color: white;
}

.field-type-option.active {
    background: #4e73df;
    color: white;
    border-color: #4e73df;
}

.field-options-container {
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-top: 1rem;
}

.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.option-item input {
    flex: 1;
    margin-right: 0.5rem;
}

.option-remove-btn {
    background: #e74a3b;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
}

.add-option-btn {
    background: #1cc88a;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    margin-top: 0.5rem;
}

/* Public Survey Form Styles */
.survey-form-container {
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    overflow: hidden;
}

.survey-header {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    padding: 2rem;
    text-align: center;
}

.survey-header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 300;
}

.survey-header p {
    margin: 1rem 0 0 0;
    opacity: 0.9;
}

.survey-body {
    padding: 2rem;
}

.survey-field {
    margin-bottom: 2rem;
}

.survey-field label {
    display: block;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.survey-field label.required::after {
    content: " *";
    color: #e74a3b;
}

.survey-field input,
.survey-field textarea,
.survey-field select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.survey-field input:focus,
.survey-field textarea:focus,
.survey-field select:focus {
    border-color: #4e73df;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.survey-field textarea {
    min-height: 100px;
    resize: vertical;
}

.survey-field .help-text {
    font-size: 0.8rem;
    color: #858796;
    margin-top: 0.25rem;
}

.survey-field .error-text {
    font-size: 0.8rem;
    color: #e74a3b;
    margin-top: 0.25rem;
}

/* Radio and Checkbox Styles */
.radio-group,
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.radio-option,
.checkbox-option {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border: 1px solid #e3e6f0;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.radio-option:hover,
.checkbox-option:hover {
    background: #f8f9fc;
    border-color: #4e73df;
}

.radio-option input,
.checkbox-option input {
    margin-right: 0.5rem;
    width: auto;
}

.radio-option.selected,
.checkbox-option.selected {
    background: #e7f3ff;
    border-color: #4e73df;
}

/* Submit Button */
.survey-submit-btn {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.35rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.survey-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(26, 200, 138, 0.3);
}

.survey-submit-btn:disabled {
    background: #858796;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Success Message */
.survey-success {
    text-align: center;
    padding: 3rem 2rem;
}

.survey-success .success-icon {
    font-size: 4rem;
    color: #1cc88a;
    margin-bottom: 1rem;
}

.survey-success h2 {
    color: #1cc88a;
    margin-bottom: 1rem;
}

.survey-success p {
    color: #858796;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .survey-form-container {
        margin: 1rem;
        border-radius: 0.35rem;
    }
    
    .survey-header {
        padding: 1.5rem;
    }
    
    .survey-header h1 {
        font-size: 1.5rem;
    }
    
    .survey-body {
        padding: 1.5rem;
    }
    
    .field-config-container {
        padding: 1rem;
    }
    
    .field-item {
        padding: 0.75rem;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Virtual Select Customization */
.vscomp-wrapper {
    margin-bottom: 0;
}

.vscomp-wrapper .vscomp-toggle-button {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem;
}

.vscomp-wrapper .vscomp-toggle-button:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Flatpickr Customization */
.flatpickr-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23858796' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M1 4h14M1 4v10a1 1 0 001 1h12a1 1 0 001-1V4M1 4V3a1 1 0 011-1h2m0 0V1m0 1h6m0-1v1m0 0h2a1 1 0 011 1v1'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}
