var dataExamine = {
    foodNameListSearch: []
};

// Hàm lấy patient_id từ URL hoặc biến global
function getPatientIdFromUrl() {
    // Thử lấy từ biến global trước
    if (typeof currentPatientId !== 'undefined' && currentPatientId) {
        return currentPatientId;
    }
    
    // Nếu không có, lấy từ URL
    const pathArray = window.location.pathname.split('/');
    const detailIndex = pathArray.indexOf('detail');
    if (detailIndex !== -1 && detailIndex + 1 < pathArray.length) {
        return pathArray[detailIndex + 1];
    }
    return null;
}

function chooseMenuExample() {
    try {
        let id = 1;
        if (menuExamine.length > 0) {
            id = menuExamine[menuExamine.length - 1].id + 1;
        }
        let menu_example_id = parseInt($("#menuExample_id").val());
        if (menu_example_id) {
            for (let menu of menuExample) {
                if (menu.id == menu_example_id) {
                    let menuNew = {
                        id: id,
                        name: menu.name_menu,
                        detail: JSON.parse(menu.detail),
                        created_at: new Date().toISOString(), // Thêm ngày tạo
                    };
                    menuExamine.push(menuNew);
                    let newItem = {
                        label: menuNew.name,
                        value: menuNew.id
                    }
                    addNewOptionToVirtualSelect('menu_id', newItem, true);
                    break;
                }
            }
            // if(isGenerate){
            $('#tb_menu tbody').empty();
            $('#tb_menu').show();
            generateTableMenu(id);
            // }
        } else {
            toarstError("Vui lòng chọn mẫu!");
        }
    } catch (error) {
        console.log('error', error);
    }
}

// Danh sách tất cả các trường có thể hiển thị (không bao gồm name vì đã cố định)
const availableColumns = {
    // Thông tin cơ bản
    'ten': { label: 'Tên tiếng Việt', group: 'basic', default: false },
    'code': { label: 'Mã thực phẩm', group: 'basic', default: false },
    'weight': { label: 'Khối lượng (g)', group: 'basic', default: true },
    
    // Chất dinh dưỡng chính (đã gộp vào food_info)
    'energy': { label: 'Năng lượng (kcal)', group: 'main_nutrients', default: true },
    'water': { label: 'Nước (g)', group: 'main_nutrients', default: false },
    'protein': { label: 'Protein (g)', group: 'main_nutrients', default: true },
    'fat': { label: 'Chất béo (g)', group: 'main_nutrients', default: true },
    'carbohydrate': { label: 'Carbohydrate (g)', group: 'main_nutrients', default: true },
    'fiber': { label: 'Chất xơ (g)', group: 'main_nutrients', default: false },
    'ash': { label: 'Tro (g)', group: 'main_nutrients', default: false },
    
    // Khoáng chất
    'calci': { label: 'Canxi (mg)', group: 'minerals', default: false },
    'phosphorous': { label: 'Phospho (mg)', group: 'minerals', default: false },
    'fe': { label: 'Sắt (mg)', group: 'minerals', default: false },
    'zinc': { label: 'Kẽm (mg)', group: 'minerals', default: false },
    'sodium': { label: 'Natri (mg)', group: 'minerals', default: false },
    'potassium': { label: 'Kali (mg)', group: 'minerals', default: false },
    'magnesium': { label: 'Magie (mg)', group: 'minerals', default: false },
    'manganese': { label: 'Mangan (mg)', group: 'minerals', default: false },
    'copper': { label: 'Đồng (mg)', group: 'minerals', default: false },
    'selenium': { label: 'Selen (μg)', group: 'minerals', default: false },
    
    // Axit béo
    'total_fat': { label: 'Tổng chất béo (g)', group: 'fatty_acids', default: false },
    'total_saturated_fat': { label: 'Axit béo bão hòa (g)', group: 'fatty_acids', default: false },
    'mufa': { label: 'Axit béo không bão hòa đơn (g)', group: 'fatty_acids', default: false },
    'fufa': { label: 'Axit béo không bão hòa đa (g)', group: 'fatty_acids', default: false },
    'cholesterol': { label: 'Cholesterol (mg)', group: 'fatty_acids', default: false },
    
    // Protein & Amino acid (food_info)
    'animal_protein': { label: 'Protein động vật (g)', group: 'amino_acids', default: false },
    'lysin': { label: 'Lysin (mg)', group: 'amino_acids', default: false },
    'methionin': { label: 'Methionin (mg)', group: 'amino_acids', default: false },
    'tryptophan': { label: 'Tryptophan (mg)', group: 'amino_acids', default: false },
    'phenylalanin': { label: 'Phenylalanin (mg)', group: 'amino_acids', default: false },
    'threonin': { label: 'Threonin (mg)', group: 'amino_acids', default: false },
    'isoleucine': { label: 'Isoleucine (mg)', group: 'amino_acids', default: false },
    'leucine': { label: 'Leucine (mg)', group: 'amino_acids', default: false },
    'valine': { label: 'Valine (mg)', group: 'amino_acids', default: false },
    
    // Vitamin
    'vitamin_a_rae': { label: 'Vitamin A (μg RAE)', group: 'vitamins', default: false },
    'vitamin_b1': { label: 'Vitamin B1 (mg)', group: 'vitamins', default: false },
    'vitamin_b2': { label: 'Vitamin B2 (mg)', group: 'vitamins', default: false },
    'vitamin_b6': { label: 'Vitamin B6 (mg)', group: 'vitamins', default: false },
    'vitamin_b12': { label: 'Vitamin B12 (μg)', group: 'vitamins', default: false },
    'vitamin_c': { label: 'Vitamin C (mg)', group: 'vitamins', default: false },
    'vitamin_e': { label: 'Vitamin E (mg)', group: 'vitamins', default: false },
    'vitamin_k': { label: 'Vitamin K (μg)', group: 'vitamins', default: false },
    'folate': { label: 'Folate (μg)', group: 'vitamins', default: false },
    
    // Đường
    'total_sugar': { label: 'Tổng đường (g)', group: 'sugars', default: false },
    'glucose': { label: 'Glucose (g)', group: 'sugars', default: false },
    'fructose': { label: 'Fructose (g)', group: 'sugars', default: false },
    'sucrose': { label: 'Sucrose (g)', group: 'sugars', default: false },
    'lactose': { label: 'Lactose (g)', group: 'sugars', default: false },
    'maltose': { label: 'Maltose (g)', group: 'sugars', default: false }
};

function generateTableMenu(menu_id) {
    try {
        if (menu_id) {
            if (menuExamine.length > 0) {
                for (let menu of menuExamine) {
                    if (menu.id == menu_id) {
                        $('#name_menu').val(menu.name);
                        $('#menu_example_note').val(menu.note || '');
                        $('#name_menu_text').text(menu.name);
                        
                        // Hiển thị thông tin ngày tạo nếu có
                        if (menu.created_at) {
                            const createdDate = new Date(menu.created_at);
                            const formattedDate = createdDate.toLocaleDateString('vi-VN') + ' ' + createdDate.toLocaleTimeString('vi-VN');
                            
                            // Tìm element để hiển thị ngày tạo
                            let dateInfo = $('#menu_date_info');
                            if (dateInfo.length === 0) {
                                // Tạo element mới nếu chưa có
                                $('#name_menu_text').after(`
                                    <small id="menu_date_info" class="text-muted d-block">
                                        <i class="fa fa-clock"></i> Tạo ngày: <span id="menu_created_date"></span>
                                    </small>
                                `);
                                dateInfo = $('#menu_date_info');
                            }
                            $('#menu_created_date').text(formattedDate);
                            dateInfo.show();
                        } else {
                            $('#menu_date_info').hide();
                        }
                        
                        // Tạo UI chọn cột hiển thị
                        createColumnSelector();
                        
                        // Load cấu hình hiển thị cột cho bệnh nhân hiện tại
                        loadTableDisplayConfig();
                        
                        addTemplateListMenuTime(menu.detail);
                        break;
                    }
                }
            }
            $('#tb_menu').show();
        } else {
            $('#tb_menu').hide();
        }
    } catch (error) {
        console.error('Error in generateTableMenu:', error);
    }
}

// Biến lưu cấu hình hiển thị cột hiện tại
let currentDisplayConfig = {
    visible_columns: ['weight', 'energy', 'protein', 'fat', 'carbohydrate'],
    column_order: ['weight', 'energy', 'protein', 'fat', 'carbohydrate']
};

// Tạo UI chọn cột hiển thị
function createColumnSelector() {
    try {
        // Kiểm tra xem đã có UI chưa
        if ($('#column_selector_container').length > 0) {
            return;
        }
        
        // Tạo container cho column selector
        const selectorHtml = `
            <div id="column_selector_container" class="mb-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center gap-2">
                                <i class="fas fa-columns"></i> Chọn cột hiển thị
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary float-right" onclick="toggleColumnSelector()">
                                <i class="fas fa-cog"></i>
                            </button>
                        </h6>
                    </div>
                    <div id="column_selector_content" class="card-body" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Chọn cột hiển thị:</h6>
                                <div id="column_checkboxes"></div>
                            </div>
                            <div class="col-md-6">
                                <h6>Thứ tự cột:</h6>
                                <div id="column_order_list" class="sortable-list"></div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-sm" onclick="applyColumnConfig()">
                                <i class="fas fa-check"></i> Áp dụng
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="resetColumnConfig()">
                                <i class="fas fa-undo"></i> Đặt lại mặc định
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="saveColumnConfig()">
                                <i class="fas fa-save"></i> Lưu cấu hình
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Thêm vào trước bảng menu
        $('#tb_menu').before(selectorHtml);
        
        // Tạo checkboxes cho từng nhóm cột
        createColumnCheckboxes();
        
    } catch (error) {
        console.error('Error creating column selector:', error);
    }
}

// Tạo checkboxes cho các cột
function createColumnCheckboxes() {
    try {
        const groups = {
            'basic': 'Thông tin cơ bản',
            'main_nutrients': 'Chất dinh dưỡng chính',
            'minerals': 'Khoáng chất',
            'fatty_acids': 'Axit béo',
            'amino_acids': 'Protein & Amino acid',
            'vitamins': 'Vitamin',
            'sugars': 'Đường'
        };
        
        let html = '';
        
        Object.keys(groups).forEach(groupKey => {
            html += `<div class="mb-3">
                <h6 class="text-secondary">${groups[groupKey]}</h6>`;
            
            Object.keys(availableColumns).forEach(columnKey => {
                const column = availableColumns[columnKey];
                if (column.group === groupKey) {
                    const isChecked = currentDisplayConfig.visible_columns.includes(columnKey) ? 'checked' : '';
                    html += `
                        <div class="form-check form-check-inline">
                            <input class="form-check-input column-checkbox" type="checkbox" 
                                   id="col_${columnKey}" value="${columnKey}" ${isChecked}
                                   onchange="updateColumnOrder()">
                            <label class="form-check-label" for="col_${columnKey}">
                                ${column.label}
                            </label>
                        </div>
                    `;
                }
            });
            
            html += '</div>';
        });
        
        $('#column_checkboxes').html(html);
        updateColumnOrderList();
        
    } catch (error) {
        console.error('Error creating column checkboxes:', error);
    }
}

// Cập nhật danh sách thứ tự cột
function updateColumnOrderList() {
    try {
        let html = '<ul class="list-group sortable" id="sortable_columns">';
        
        currentDisplayConfig.visible_columns.forEach(columnKey => {
            const column = availableColumns[columnKey];
            if (column) {
                html += `
                    <li class="list-group-item d-flex justify-content-between align-items-center" data-column="${columnKey}">
                        <span><i class="fas fa-grip-vertical text-muted mr-2"></i>${column.label}</span>
                        <small class="text-muted">${columnKey}</small>
                    </li>
                `;
            }
        });
        
        html += '</ul>';
        $('#column_order_list').html(html);
        
        // Khởi tạo sortable nếu có jQuery UI
        if (typeof $.fn.sortable !== 'undefined') {
            $('#sortable_columns').sortable({
                update: function(event, ui) {
                    updateColumnOrderFromList();
                }
            });
        }
        
    } catch (error) {
        console.error('Error updating column order list:', error);
    }
}

// Cập nhật thứ tự cột từ danh sách
function updateColumnOrderFromList() {
    try {
        const newOrder = [];
        $('#sortable_columns li').each(function() {
            newOrder.push($(this).data('column'));
        });
        currentDisplayConfig.column_order = newOrder;
        currentDisplayConfig.visible_columns = newOrder;
        
    } catch (error) {
        console.error('Error updating column order from list:', error);
    }
}

// Cập nhật thứ tự cột khi checkbox thay đổi
function updateColumnOrder() {
    try {
        const checkedColumns = [];
        $('.column-checkbox:checked').each(function() {
            checkedColumns.push($(this).val());
        });
        
        // Giữ thứ tự hiện tại cho các cột đã chọn, thêm cột mới vào cuối
        const newVisibleColumns = [];
        
        // Thêm các cột đã có theo thứ tự cũ
        currentDisplayConfig.visible_columns.forEach(col => {
            if (checkedColumns.includes(col)) {
                newVisibleColumns.push(col);
            }
        });
        
        // Thêm các cột mới
        checkedColumns.forEach(col => {
            if (!newVisibleColumns.includes(col)) {
                newVisibleColumns.push(col);
            }
        });
        
        currentDisplayConfig.visible_columns = newVisibleColumns;
        currentDisplayConfig.column_order = newVisibleColumns;
        
        updateColumnOrderList();
        
    } catch (error) {
        console.error('Error updating column order:', error);
    }
}

// Toggle hiển thị column selector
function toggleColumnSelector() {
    $('#column_selector_content').slideToggle();
}

// Áp dụng cấu hình cột
function applyColumnConfig() {
    try {
        // Rebuild table với cấu hình mới
        rebuildTableWithNewColumns();
        
        // Ẩn column selector
        $('#column_selector_content').slideUp();
        
        Swal.fire({
            icon: 'success',
            title: 'Thành công!',
            text: 'Đã áp dụng cấu hình hiển thị cột mới.',
            timer: 2000,
            showConfirmButton: false
        });
        
    } catch (error) {
        console.error('Error applying column config:', error);
        Swal.fire({
            icon: 'error',
            title: 'Lỗi!',
            text: 'Có lỗi xảy ra khi áp dụng cấu hình.'
        });
    }
}

// Đặt lại cấu hình mặc định
function resetColumnConfig() {
    try {
        // Lấy các cột mặc định
        const defaultColumns = Object.keys(availableColumns).filter(key => availableColumns[key].default);
        
        currentDisplayConfig = {
            visible_columns: defaultColumns,
            column_order: defaultColumns
        };
        
        // Cập nhật UI
        $('.column-checkbox').prop('checked', false);
        defaultColumns.forEach(col => {
            $(`#col_${col}`).prop('checked', true);
        });
        
        updateColumnOrderList();
        
        Swal.fire({
            icon: 'info',
            title: 'Đã đặt lại!',
            text: 'Cấu hình đã được đặt lại về mặc định.',
            timer: 2000,
            showConfirmButton: false
        });
        
    } catch (error) {
        console.error('Error resetting column config:', error);
    }
}

// Lưu cấu hình vào database
function saveColumnConfig() {
    try {
        const patientId = getPatientIdFromUrl();
        if (!patientId) {
            Swal.fire({
                icon: 'warning',
                title: 'Cảnh báo!',
                text: 'Không tìm thấy thông tin bệnh nhân để lưu cấu hình.'
            });
            return;
        }
        
        $.ajax({
            url: '/khau-phan-an/save-table-config',
            method: 'POST',
            data: {
                patient_id: patientId,
                config: JSON.stringify(currentDisplayConfig)
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Thành công!',
                        text: 'Đã lưu cấu hình hiển thị cột.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi!',
                        text: response.message || 'Có lỗi xảy ra khi lưu cấu hình.'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi!',
                    text: 'Có lỗi xảy ra khi kết nối server.'
                });
            }
        });
        
    } catch (error) {
        console.error('Error saving column config:', error);
    }
}

// Load cấu hình hiển thị từ database
function loadTableDisplayConfig() {
    try {
        const patientId = getPatientIdFromUrl();
        if (!patientId) {
            return;
        }
        
        $.ajax({
            url: '/khau-phan-an/get-table-config',
            method: 'GET',
            data: { patient_id: patientId },
            success: function(response) {
                if (response.success && response.data && response.data.table_display_config) {
                    try {
                        const config = JSON.parse(response.data.table_display_config);
                        if (config.visible_columns && config.column_order) {
                            currentDisplayConfig = config;
                            
                            // Cập nhật UI nếu đã tạo
                            if ($('#column_checkboxes').length > 0) {
                                $('.column-checkbox').prop('checked', false);
                                config.visible_columns.forEach(col => {
                                    $(`#col_${col}`).prop('checked', true);
                                });
                                updateColumnOrderList();
                            }
                        }
                    } catch (e) {
                        console.log('Error parsing config, using default');
                    }
                }
            },
            error: function() {
                console.log('Error loading config, using default');
            }
        });
        
    } catch (error) {
        console.error('Error loading table display config:', error);
    }
}

function addTemplateListMenuTime(listMenuTime) {
    try {
        // Xóa nội dung cũ
        $("#tb_menu").find('tbody').empty();
        
        // Cập nhật table header với cấu hình hiện tại
        updateTableHeaderWithConfig();
        
        if (listMenuTime.length > 0) {
            let listFoodTotal = [];
            for (let item of listMenuTime) {
                let menuTime = addTemplateMenuTime(item);
                $("#tb_menu").find('tbody').append(menuTime);
                if (item.listFood.length > 0) {
                    for (let food of item.listFood) {
                        let foodTemplate = addFoodTemplate(food, item.id);
                        $("#tb_menu").find('tbody').append(foodTemplate);
                    }
                    listFoodTotal.push(...item.listFood);
                }
            }
            setTotalMenu(listFoodTotal);
        }
    } catch (error) {
        console.error('Error in addTemplateListMenuTime:', error);
    }
}

// Cập nhật table header với cấu hình cột mới (chỉ cập nhật label, không thay đổi cấu trúc)
function updateTableHeaderWithConfig() {
    try {
        // Tạo header mới với cấu trúc cố định + cột động
        let headerHtml = `
                <tr>
                    <th class="text-center">Bữa ăn</th>
                    <th class="text-center">Tên món ăn</th>
        `;
        
        // Thêm header cho các cột thông tin thực phẩm được chọn
        currentDisplayConfig.visible_columns.forEach(columnKey => {
            const column = availableColumns[columnKey];
            if (column) {
                headerHtml += `<th class="text-center">${column.label}</th>`;
            }
        });
        
        headerHtml += `
                    <th class="text-center">Thao tác</th>
                </tr>
        `;
        
        // Cập nhật header - chỉ thay đổi nội dung trong thead, không tạo thead mới
        $('#tb_menu thead').html(headerHtml);
        
    } catch (error) {
        console.error('Error updating table header:', error);
    }
}

// Rebuild table với cấu hình cột mới
function rebuildTableWithNewColumns() {
    try {
        const currentMenuId = parseInt($('#menu_id').val());
        if (currentMenuId) {
            // Tìm menu hiện tại và rebuild
            for (let menu of menuExamine) {
                if (menu.id == currentMenuId) {
                    // Rebuild với cấu hình mới
                    addTemplateListMenuTime(menu.detail);
                    break;
                }
            }
        }
    } catch (error) {
        console.error('Error rebuilding table with new columns:', error);
    }
}

function addTemplateMenuTime(menuTime) {
    try {
        let status = parseInt($('#status_examine').val());
        let rowspan = menuTime.listFood.length + 1;
        let colspanCount = currentDisplayConfig.visible_columns.length + 1; // +1 cho cột thao tác
        
        return $('<tr/>')
            .attr("id", "menu_time_" + menuTime.id)
            .addClass("text-center")
            .append($("<td/>")
                .css({ "writing-mode": "vertical-rl" })
                .attr("rowspan", rowspan)
                .text(menuTime.name)
            )
            .append($("<td/>")
                .append($("<input/>")
                    .attr({ "type": "text", "value": menuTime.name_course, placeholder: "Nhập tên món ăn"})
                    .addClass("form-control form-control-title p-1")
                    .css({ "text-align": "center" })
                    .data("menu_time_id", menuTime.id)
                    .change(function () {
                        let id = $(this).data('menu_time_id');
                        changeCourse(id);
                    })
                )
                .attr("colspan", colspanCount)
            );
    } catch (error) {
        console.error('Error in addTemplateMenuTime:', error);
    }
}

function addFoodTemplate(food, menuTime_id) {
    try {
        let $row = $('<tr/>').attr("id", "food_" + menuTime_id + "_" + food.id);
        
        // Cột tên món ăn (cố định)
        $row.append($("<td/>").text(food.name || ''));
        
        // Các cột thông tin thực phẩm theo cấu hình
        currentDisplayConfig.visible_columns.forEach(columnKey => {
            let $cell = $("<td/>").attr("id", "food_" + menuTime_id + "_" + food.id + "_" + columnKey);
            
            // Xử lý đặc biệt cho cột weight (có input)
            if (columnKey === 'weight') {
                $cell.append($("<input/>")
                    .attr({ "type": "number", "value": food[columnKey] || 0 })
                    .addClass("form-control form-control-title p-1")
                    .data("food_id", food.id)
                    .data("menu_time_id", menuTime_id)
                    .change(function () {
                        let idFood = $(this).data('food_id');
                        let idMenuTime = $(this).data('menu_time_id');
                        let weight = $(this).val();
                        changeWeightFood(idFood, idMenuTime, weight);
                    })
                );
            } else {
                // Hiển thị giá trị với định dạng phù hợp
                let value = food[columnKey];
                if (value !== null && value !== undefined && value !== '') {
                    // Làm tròn số thập phân nếu là số
                    if (!isNaN(value) && value !== '') {
                        value = parseFloat(value).toFixed(2);
                        // Loại bỏ số 0 thừa ở cuối
                        value = parseFloat(value).toString();
                    }
                    $cell.text(value);
                } else {
                    $cell.text('0');
                }
            }
            
            $row.append($cell);
        });
        
        // Cột thao tác (cố định)
        $row.append($("<td/>")
            .append($(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" width=".8rem" heigh=".8rem">
                    <path d="M310.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 210.7 54.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L114.7 256 9.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 301.3 265.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L205.3 256 310.6 150.6z"/>
                </svg>`)
                .css({ "display":'block' }))
            .css({ "cursor": "pointer"})
            .data("food_id", food.id)
            .data("menu_time_id", menuTime_id)
            .click(function () {
                let idFood = $(this).data('food_id');
                let idMenuTime = $(this).data('menu_time_id');
                deleteFood(idFood, idMenuTime);
            })
        );
        
        return $row;
        
    } catch (error) {
        console.error('Error in addFoodTemplate:', error);
        return $('<tr/>'); // Trả về row trống nếu có lỗi
    }
}

function deleteFood(id_food, menuTime_id) {
    try {
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of menuExamine) {
            if (menu_id == menu.id) {
                let listFoodTotal = [];
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        // Xóa thực phẩm khỏi danh sách
                        item.listFood = item.listFood.filter(food => food.id !== id_food);
                        
                        // Xóa row khỏi table
                        $('#food_' + menuTime_id + '_' + id_food).remove();
                        
                        // Cập nhật rowspan cho menu time
                        $('#menu_time_' + menuTime_id + ' td:first-child').attr('rowspan', (item.listFood.length + 1));
                    }
                    listFoodTotal.push(...item.listFood);
                }
                setTotalMenu(listFoodTotal);
                break;
            }
        }
    } catch (error) {
        console.error('Error deleting food:', error);
    }
}

function setTotalMenu(listFood) {
    try {
        if (listFood.length > 0) {
            // Khởi tạo object để lưu tổng các cột
            const totals = {};
            
            // Tính tổng cho tất cả các cột được hiển thị
            currentDisplayConfig.visible_columns.forEach(columnKey => {
                totals[columnKey] = 0;
            });
            
            // Tính tổng cho từng thực phẩm
            for (let food of listFood) {
                currentDisplayConfig.visible_columns.forEach(columnKey => {
                    if (food[columnKey] !== undefined && food[columnKey] !== null && food[columnKey] !== '') {
                        const value = parseFloat(food[columnKey]) || 0;
                        totals[columnKey] += value;
                    }
                });
            }
            
            // Tạo hoặc cập nhật dòng tổng
            createOrUpdateTotalRow(totals);
            
            // Tính phần trăm cho các chất dinh dưỡng chính (nếu có energy)
            if (totals.energy && totals.energy > 0) {
                if (totals.protein) {
                    const protein_percent = (totals.protein * 4 * 100) / totals.energy;
                    $('#total_protein_percent').text(protein_percent.toFixed(2) + '%');
                }
                if (totals.fat) {
                    const fat_percent = (totals.fat * 9 * 100) / totals.energy;
                    $('#total_fat_percent').text(fat_percent.toFixed(2) + '%');
                }
                if (totals.carbohydrate) {
                    const carb_percent = (totals.carbohydrate * 4 * 100) / totals.energy;
                    $('#total_carbohydrate_percent').text(carb_percent.toFixed(2) + '%');
                }
            }
            
        } else {
            // Xóa dòng tổng nếu không có thực phẩm
            $('#total_row').remove();
        }
    } catch (error) {
        console.error('Error in setTotalMenu:', error);
    }
}

// Tạo hoặc cập nhật dòng tổng
function createOrUpdateTotalRow(totals) {
    try {
        // Xóa dòng tổng cũ nếu có
        $('#total_row').remove();
        
        // Tạo dòng tổng mới
        let $totalRow = $('<tr id="total_row" class="table-info font-weight-bold">');
        
        // Cột bữa ăn (cố định)
        $totalRow.append($('<td class="text-center">').text('TỔNG'));
        
        // Cột tên món ăn (cố định)
        $totalRow.append($('<td class="text-center">').text(''));
        
        // Các cột thông tin thực phẩm theo cấu hình
        currentDisplayConfig.visible_columns.forEach(columnKey => {
            let $cell = $('<td class="text-center">');
            
            if (columnKey === 'weight') {
                // Cột weight không tính tổng
                $cell.text('');
            } else {
                const total = totals[columnKey] || 0;
                let displayValue = '';
                
                if (total > 0) {
                    // Làm tròn và hiển thị
                    displayValue = parseFloat(total.toFixed(2)).toString();
                }
                
                $cell.text(displayValue);
                $cell.attr('id', `total_${columnKey}`);
            }
            
            $totalRow.append($cell);
        });
        
        // Cột thao tác (cố định)
        $totalRow.append($('<td class="text-center">').text(''));
        
        // Thêm dòng tổng vào cuối bảng
        $('#tb_menu tbody').append($totalRow);
        
    } catch (error) {
        console.error('Error creating total row:', error);
    }
}

function changeCourse(menuTime_id) {
    try {
        let name_course = $('#menu_time_' + menuTime_id).find("input").val();
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of menuExamine) {
            if (menu_id == menu.id) {
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        item.name_course = name_course;
                        break;
                    }
                }
                break;
            }
        }
    } catch (error) {

    }
}

// Hàm cập nhật note cho menu hiện tại
function updateMenuNote() {
    try {
        let menu_id = parseInt($('#menu_id').val());
        let note = $('#menu_example_note').val();
        for (let menu of menuExamine) {
            if (menu_id == menu.id) {
                menu.note = note;
                break;
            }
        }
    } catch (error) {
        console.error('Error updating menu note:', error);
    }
}

function changeWeightFood(id_food, menuTime_id, value) {
    try {
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of menuExamine) {
            if (menu_id == menu.id) {
                let listFoodTotal = [];
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        for (let food of item.listFood) {
                            if (id_food == food.id) {
                                caculateFoodInfo(food, value);
                                
                                // Cập nhật tất cả các cột được hiển thị
                                currentDisplayConfig.visible_columns.forEach(columnKey => {
                                    if (columnKey !== 'weight') { // weight đã được cập nhật qua input
                                        const $cell = $("#food_" + menuTime_id + "_" + food.id + "_" + columnKey);
                                        if ($cell.length > 0) {
                                            let value = food[columnKey];
                                            if (value !== null && value !== undefined && value !== '') {
                                                // Làm tròn số thập phân nếu là số
                                                if (!isNaN(value) && value !== '') {
                                                    value = parseFloat(value).toFixed(2);
                                                    // Loại bỏ số 0 thừa ở cuối
                                                    value = parseFloat(value).toString();
                                                }
                                                $cell.text(value);
                                            } else {
                                                $cell.text('0');
                                            }
                                        }
                                    }
                                });
                                
                                break;
                            }
                        }
                    }
                    listFoodTotal.push(...item.listFood);
                }
                setTotalMenu(listFoodTotal);
                break;
            }
        }
    } catch (error) {
        console.error('Error in changeWeightFood:', error);
    }
}

function caculateFoodInfo(food, weight) {
    try {
        if (food && weight > 0) {
            const originalWeight = food.weight;
            const ratio = weight / originalWeight;
            
            // Tính toán lại các trường cơ bản (giữ nguyên để tương thích)
            food.energy = Math.round((food.energy * ratio));
            food.protein = Math.round(((food.protein * ratio)) * 100) / 100;
            food.animal_protein = Math.round(((food.animal_protein * ratio)) * 100) / 100;
            food.lipid = Math.round(((food.lipid * ratio)) * 100) / 100;
            food.unanimal_lipid = Math.round(((food.unanimal_lipid * ratio)) * 100) / 100;
            food.carbohydrate = Math.round(((food.carbohydrate * ratio)) * 100) / 100;
            
            // Tính toán lại tất cả các trường dinh dưỡng khác (nếu có)
            if (food.fat !== undefined) food.fat = Math.round(((food.fat * ratio)) * 100) / 100;
            if (food.fiber !== undefined) food.fiber = Math.round(((food.fiber * ratio)) * 100) / 100;
            if (food.ash !== undefined) food.ash = Math.round(((food.ash * ratio)) * 100) / 100;
            if (food.water !== undefined) food.water = Math.round(((food.water * ratio)) * 100) / 100;
            if (food.calci !== undefined) food.calci = Math.round(((food.calci * ratio)) * 100) / 100;
            if (food.phosphorous !== undefined) food.phosphorous = Math.round(((food.phosphorous * ratio)) * 100) / 100;
            if (food.fe !== undefined) food.fe = Math.round(((food.fe * ratio)) * 100) / 100;
            if (food.zinc !== undefined) food.zinc = Math.round(((food.zinc * ratio)) * 100) / 100;
            if (food.sodium !== undefined) food.sodium = Math.round(((food.sodium * ratio)) * 100) / 100;
            if (food.potassium !== undefined) food.potassium = Math.round(((food.potassium * ratio)) * 100) / 100;
            if (food.magnesium !== undefined) food.magnesium = Math.round(((food.magnesium * ratio)) * 100) / 100;
            
            // Vitamin
            if (food.vitamin_a_rae !== undefined) food.vitamin_a_rae = Math.round(((food.vitamin_a_rae * ratio)) * 100) / 100;
            if (food.vitamin_b1 !== undefined) food.vitamin_b1 = Math.round(((food.vitamin_b1 * ratio)) * 100) / 100;
            if (food.vitamin_b2 !== undefined) food.vitamin_b2 = Math.round(((food.vitamin_b2 * ratio)) * 100) / 100;
            if (food.vitamin_b6 !== undefined) food.vitamin_b6 = Math.round(((food.vitamin_b6 * ratio)) * 100) / 100;
            if (food.vitamin_b12 !== undefined) food.vitamin_b12 = Math.round(((food.vitamin_b12 * ratio)) * 100) / 100;
            if (food.vitamin_c !== undefined) food.vitamin_c = Math.round(((food.vitamin_c * ratio)) * 100) / 100;
            if (food.vitamin_e !== undefined) food.vitamin_e = Math.round(((food.vitamin_e * ratio)) * 100) / 100;
            if (food.vitamin_k !== undefined) food.vitamin_k = Math.round(((food.vitamin_k * ratio)) * 100) / 100;
            if (food.folate !== undefined) food.folate = Math.round(((food.folate * ratio)) * 100) / 100;
            if (food.niacin !== undefined) food.niacin = Math.round(((food.niacin * ratio)) * 100) / 100;
            
            // Amino acids
            if (food.lysin !== undefined) food.lysin = Math.round(((food.lysin * ratio)) * 100) / 100;
            if (food.methionin !== undefined) food.methionin = Math.round(((food.methionin * ratio)) * 100) / 100;
            if (food.tryptophan !== undefined) food.tryptophan = Math.round(((food.tryptophan * ratio)) * 100) / 100;
            if (food.phenylalanin !== undefined) food.phenylalanin = Math.round(((food.phenylalanin * ratio)) * 100) / 100;
            if (food.threonin !== undefined) food.threonin = Math.round(((food.threonin * ratio)) * 100) / 100;
            if (food.isoleucine !== undefined) food.isoleucine = Math.round(((food.isoleucine * ratio)) * 100) / 100;
            if (food.arginine !== undefined) food.arginine = Math.round(((food.arginine * ratio)) * 100) / 100;
            if (food.histidine !== undefined) food.histidine = Math.round(((food.histidine * ratio)) * 100) / 100;
            if (food.valine !== undefined) food.valine = Math.round(((food.valine * ratio)) * 100) / 100;
            if (food.leucine !== undefined) food.leucine = Math.round(((food.leucine * ratio)) * 100) / 100;
            
            // Fatty acids
            if (food.total_fat !== undefined) food.total_fat = Math.round(((food.total_fat * ratio)) * 100) / 100;
            if (food.total_saturated_fat !== undefined) food.total_saturated_fat = Math.round(((food.total_saturated_fat * ratio)) * 100) / 100;
            if (food.mufa !== undefined) food.mufa = Math.round(((food.mufa * ratio)) * 100) / 100;
            if (food.oleic !== undefined) food.oleic = Math.round(((food.oleic * ratio)) * 100) / 100;
            if (food.linoleic !== undefined) food.linoleic = Math.round(((food.linoleic * ratio)) * 100) / 100;
            if (food.trans_fatty_acids !== undefined) food.trans_fatty_acids = Math.round(((food.trans_fatty_acids * ratio)) * 100) / 100;
            if (food.cholesterol !== undefined) food.cholesterol = Math.round(((food.cholesterol * ratio)) * 100) / 100;
            
            // Cập nhật khối lượng cuối cùng
            food.weight = weight;
        }
    } catch (error) {
        console.error('Error in caculateFoodInfo:', error);
    }
}

function generateFoodName(id) {
    // Khởi tạo Virtual Select với onServerSearch
    VirtualSelect.init({
        ele: '#' + id,
        options: [],
        search: true,
        searchPlaceholderText: 'Tìm kiếm thực phẩm (tối thiểu 2 ký tự)...',
        placeholder: 'Chọn thực phẩm',
        noOptionsText: 'Không có kết quả được tìm thấy',
        noSearchResultsText: 'Không tìm thấy kết quả phù hợp',
        searchingText: 'Đang tìm kiếm...',
        allowNewOption: false,
        hasOptionDescription: true,
        showSelectedOptionsFirst: true,
        maxValues: 1,
        searchDelay: 500,
        onServerSearch: function(searchValue, virtualSelect) {
            if (searchValue && searchValue.length >= 2) {
                // Sử dụng API chung cho cả admin và user
                const typeSelect = document.querySelector('#food_type');
                const yearSelect = document.querySelector('#food_year');
                let apiUrl = '/api/food-search';
                
                // Thêm filter parameters
                let params = [`search=${encodeURIComponent(searchValue)}`];
                if (typeSelect && typeSelect.value) {
                    params.push(`type=${typeSelect.value}`);
                }
                if (yearSelect && yearSelect.value) {
                    params.push(`type_year=${yearSelect.value}`);
                }
                
                if (params.length > 0) {
                    apiUrl += '?' + params.join('&');
                }
                
                fetch(apiUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            
                            // Chuyển đổi dữ liệu cho Virtual Select
                            const options = data.data.map(item => {
                                const foodData = item.customData;
                                return {
                                    label: item.label,
                                    value: item.value,
                                    description: `Năng lượng: ${foodData.energy || 'N/A'} | Protein: ${foodData.protein || 'N/A'}`,
                                    customData: foodData
                                };
                            });
                            
                            // Cập nhật options cho Virtual Select
                            virtualSelect.setServerOptions(options);
                        } else {
                            console.log('API returned error:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Lỗi khi tìm kiếm:', error);
                    });
            } else {
                console.log('Search value too short:', searchValue);
            }
        },
        onChange: function(value) {
            // Xử lý khi có sự thay đổi lựa chọn
            if (value) {
                const selectedOption = this.getSelectedOptions()[0];
                if (selectedOption && selectedOption.customData) {
                    
                }
            }
        }
    });
}



// Hàm hỗ trợ để lấy dữ liệu chi tiết của thực phẩm đã chọn
function getSelectedFoodData(id) {
    const virtualSelect = document.querySelector('#' + id).virtualSelect;
    const selectedOptions = virtualSelect.getSelectedOptions();
    
    if (selectedOptions.length > 0) {
        return selectedOptions[0].customData;
    }
    return null;
}

// Hàm để xóa lựa chọn (tương đương allowClear của Select2)
function clearFoodSelection(id) {
    const virtualSelect = document.querySelector('#' + id).virtualSelect;
    virtualSelect.reset();
}

// Cập nhật dropdown thực phẩm khi thay đổi filter
function updateFoodDropdown(selectId) {
    const virtualSelectElement = document.querySelector('#' + selectId);
    if (virtualSelectElement && virtualSelectElement.virtualSelect) {
        // Reset lựa chọn hiện tại
        virtualSelectElement.virtualSelect.reset();
        // Xóa options hiện tại
        virtualSelectElement.virtualSelect.setServerOptions([]);
    }
}

function showConfirmSaveMenu() {
    try {
        $('#modal-cf-save-menu').modal('show');
    } catch (error) {

    }
}

function saveMenu() {
    try {
        let url = '/khau-phan-an/save-menu';
        let menu_id = parseInt($('#menu_id').val());
        // Lấy patient_id từ URL hoặc từ biến global
        let patient_id = getPatientIdFromUrl();
        let data = { 
            patient_id: patient_id,
            note: $('#menu_example_note').val(),
            detail: JSON.stringify(menuExamine)
        };

        $.ajax({
            type: 'POST',
            url: url,
            data: data,
            beforeSend: function () {
                $('#modal-cf-save-menu').modal('hide');
                loading.show();
            },
            success: function (result) {
                loading.hide();
                if (result.success) {
                    toarstMessage('Lưu thực đơn thành công');
                } else {
                    toarstError(result.message);
                }
            },
            error: function (jqXHR, exception) {
                loading.hide();
                ajax_call_error(jqXHR, exception);
            }
        });
    } catch (error) {

    }
}

// Hàm lưu thực đơn dành cho admin
function saveMenuExample() {
    try {
        const menuName = $('#name_menu_text').val() || 'Thực đơn mới';
        const menuNote = $('#menu_example_note').val() || '';
        
        // Lấy chi tiết thực đơn từ menuExamine
        let detail = [];
        if (window.menuExamine && window.menuExamine.length > 0) {
            detail = window.menuExamine[0].detail || [];
        }

        const data = {
            name_menu: menuName,
            detail: JSON.stringify(detail),
            share: 1, // Mặc định chia sẻ
            note: menuNote
        };

        // Nếu đang edit thì thêm id
        if (window.menuExamine && window.menuExamine.length > 0 && window.menuExamine[0].isExisting) {
            data.id = window.menuExamine[0].id;
        }

        $.ajax({
            type: 'POST',
            url: '/admin/thuc-don-mau/upsert/',
            data: data,
            beforeSend: function () {
                if (typeof loading !== 'undefined') loading.show();
            },
            success: function (result) {
                if (typeof loading !== 'undefined') loading.hide();
                if (result.success) {
                    toarstMessage(result.message || 'Lưu thành công!');
                    
                    // Nếu là tạo mới thì chuyển sang trang edit
                    if (!window.menuExamine || window.menuExamine.length === 0 || !window.menuExamine[0].isExisting) {
                        if (result.data && result.data.id) {
                            setTimeout(() => {
                                window.location.href = '/admin/thuc-don-mau/' + result.data.id;
                            }, 1000);
                        }
                    } else {
                        // Cập nhật thông tin hiện tại
                        window.menuExamine[0].name = menuName;
                        $('#name_menu_text').val(menuName);
                    }
                } else {
                    toarstError(result.message || 'Có lỗi xảy ra khi lưu thực đơn!');
                }
            },
            error: function (jqXHR, exception) {
                if (typeof loading !== 'undefined') loading.hide();
                console.error('Error saving menu:', jqXHR, exception);
                toarstError('Có lỗi xảy ra khi lưu thực đơn!');
            }
        });
    } catch (error) {
        console.error('Error in saveMenuExample:', error);
        toarstError('Có lỗi xảy ra!');
    }
}

function addMenu() {
    try {
        //thêm menu trống
        let menuNew = addMenuList();
        menuExamine.push(menuNew);
        
        //thêm vào virtual select
        const newOption = {
            label: menuNew.name,
            value: menuNew.id,
            customData: menuNew
        };
        
        const element = document.querySelector('#menu_id');
        if (element) {
            element.addOption(newOption);
            element.setValue(menuNew.id);
        }
        
        resetTemplateMenu();
        //tạo template menu
        generateTableMenu(menuNew.id);
        $('#name_menu_text').val(menuNew.name);
        $('#tb_menu').show();
        
        // Hiển thị thông tin ngày tạo cho thực đơn mới
        if (menuNew.created_at) {
            const createdDate = new Date(menuNew.created_at);
            const formattedDate = createdDate.toLocaleDateString('vi-VN') + ' ' + createdDate.toLocaleTimeString('vi-VN');
            
            let dateInfo = $('#menu_date_info');
            if (dateInfo.length === 0) {
                $('#name_menu_text').after(`
                    <small id="menu_date_info" class="text-muted d-block">
                        <i class="fa fa-clock"></i> Tạo ngày: <span id="menu_created_date"></span>
                    </small>
                `);
                dateInfo = $('#menu_date_info');
            }
            $('#menu_created_date').text(formattedDate);
            dateInfo.show();
        }
    } catch (error) {
        console.error(error);
    }
}

function addMenuList() {
    let id = 1;
    if (menuExamine.length > 0) {
        id = menuExamine[menuExamine.length - 1].id + 1;
    }
    let menu = {
        id: id,
        name: "Thực đơn " + id,
        detail: [],
        note: '',
        created_at: new Date().toISOString(), // Thêm ngày tạo
    }
    for (let time of listMenuTime) {
        menu.detail.push({
            "id": time.id,
            "name": time.name,
            "name_course": '',
            "listFood": []
        });
    }
    return menu;
}

function resetTemplateMenu() {
    //Xóa template menu hiện tại
    $('#tb_menu').find('tbody').empty();
    $('#menu_example_note').val('');
    $('#total_energy').text('');
    $('#total_protein').text('');
    $('#total_animal_protein').text('');
    $('#total_lipid').text('');
    $('#total_unanimal_lipid').text('');
    $('#total_carbohydrate').text('');

    $('#total_protein_percent').text('');
    $('#total_lipid_percent').text('');
    $('#total_carbohydrate_percent').text('');
}

// Thêm thực phẩm vào thực đơn
function addFoodToMenu() {
    try {
        // Kiểm tra xem có phải trang admin không
        const isAdminPage = window.location.pathname.includes('/admin/thuc-don-mau/');
        
        if (isAdminPage) {
            return addFoodToMenuAdmin();
        }
        
        const menuSelect = document.querySelector('#menu_id');
        const menuTimeSelect = document.querySelector('#menuTime_id');
        const foodSelect = document.querySelector('#food_name');
        const selectedFoodOptions = foodSelect.getSelectedOptions();
        const menu_id = menuSelect.value;
        if (menu_id && menuExamine.length > 0) {
            for (let item of menuExamine) {
                if (menu_id == item.id) {
                    const menuTime_id = menuTimeSelect.value;
                    if (menuTime_id) {
                        if (item.detail.length > 0) {
                            let listFoodTotal = [];
                            for (let menuTime of item.detail) {
                                if (menuTime_id == menuTime.id) {
                                    const selectedFoodValue = foodSelect.value;
                                    if (!selectedFoodValue || !selectedFoodOptions) {
                                        toarstError('Vui lòng chọn thực phẩm!');
                                        return;
                                    }
                                    
                                    // Kiểm tra selectedFoodOptions[0] cho user page  
                                    const selectedOption = selectedFoodOptions;
                                    if (!selectedOption || !selectedOption.customData) {
                                        toarstError('Thông tin dinh dưỡng của thực phẩm không có sẵn!');
                                        return;
                                    }
                                    
                                    const foodData = selectedOption.customData;
                                    let id = menuTime.listFood.length == 0 ? 1 : menuTime.listFood[menuTime.listFood.length - 1].id + 1;
                                    const weight = parseInt($('#weight_food').val()) || 0;
                                    let food = {
                                        "id": id,
                                        "id_food": selectedOption.value,
                                        "name": selectedOption.label,
                                        "weight": weight,
                                        // Làm phẳng toàn bộ thông tin thực phẩm (spread operator)
                                        ...foodData
                                    }
                                    
                                    // Tính toán lại các giá trị dựa trên khối lượng
                                    if (weight > 0) {
                                        caculateFoodInfo(food, weight);
                                    }
                                    menuTime.listFood.push(food);
                                    let foodTemplate = addFoodTemplate(food, menuTime_id);
                                    if (id == 1) {
                                        foodTemplate.insertAfter('#menu_time_' + menuTime_id);
                                    } else {
                                        foodTemplate.insertAfter('#food_' + menuTime_id + "_" + (id - 1))
                                    }
                                    $('#menu_time_' + menuTime_id + ' td:first-child').attr('rowspan', (id + 1));
                                }
                                listFoodTotal.push(...menuTime.listFood);
                            }
                            setTotalMenu(listFoodTotal);
                            // Reset food selection
                            foodSelect.reset();
                            $('#weight_food').val('');
                        } else {
                            toarstError('Chưa có dữ liệu giờ ăn!');
                        }
                    } else {
                        toarstError('Bạn chưa chọn giờ ăn!');
                    }
                    break;
                }
            }
        } else {
            toarstError('Tạo mới hoặc chọn menu mẫu!');
        }
    } catch (error) {
        console.error(error);
    }
}

function addNewOptionToVirtualSelect(selectId, newOption, isSetValue = false) {
    const element = document.querySelector('#' + selectId);
    
    // Kiểm tra xem Virtual Select instance có tồn tại không
    if (typeof(element.virtualSelect) == 'undefined') {
        console.error('Virtual Select instance not found for element:', selectId);
        return;
    }
    
    // Sử dụng method addOption của Virtual Select
    element.addOption(newOption);
    if(isSetValue){
        // Set giá trị được chọn (tương đương selected: true)
        element.setValue(newOption.value);
    }
}

function generateMenuExamine() {
    if (menuExamine && menuExamine.length > 0) {
        const menuSelect = document.querySelector('#menu_id');
        if (menuSelect && menuSelect.virtualSelect) {
            // Virtual Select đã được khởi tạo từ view với dữ liệu menuExamine
            // Chỉ cần set value cho item cuối cùng và tính toán tổng
            
            const lastItem = menuExamine[menuExamine.length - 1];
            if (lastItem) {
                // Set value cho item cuối cùng
                menuSelect.setValue(lastItem.id);
            }
            
            // Tính toán tổng cho item đầu tiên (nếu có)
            if (menuExamine.length > 0) {
                const firstItem = menuExamine[0];
                let listFoodTotal = [];
                for (let menuTime of firstItem.detail) {
                    if (menuTime.listFood && menuTime.listFood.length > 0) {
                        listFoodTotal.push(...menuTime.listFood);
                    }
                }
                setTotalMenu(listFoodTotal);
            }
            
            // Generate table với menu đã được chọn
            let menu_id = menuSelect.value || (menuExamine.length > 0 ? menuExamine[0].id : 0);
            generateTableMenu(menu_id);
        }
    }
}

 function showModalDeleteMenuExample(val) {
    try {
        var confirmBox = `
        <div class="modal fade" id="modal_cf_delete_example" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="text-center mb-2">
                    <i class="fa-solid fa-trash"></i>
                </div>
                <h4 class="modal-title text-center text-tra-lai mb-4">Bỏ thực đơn mẫu</h4>
                <p class="text-body-2 fw-5 text-center mb-4">Bạn muốn bỏ thực đơn mẫu này khỏi phiếu khám không?</p>
                <div class="row g-2 justify-content-center">
                <div class="col-6">
                    <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
                </div>
                <div class="col-6">
                    <button onclick="deleteMenuExample(`+ val + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                     Đồng ý
                    </button>
                </div>
                </div>
            </div>
            </div>
        </div>`;
        $("#modal_confirm_box").html(confirmBox);
        $("#modal_cf_delete_example").modal('show');
    } catch (error) {

    }
}

function deleteMenuExample(id) {
    try {
        // Xóa thực đơn khỏi list
        removeItemArrayByIdObject(menuExamine, id);
        document.querySelector('#menu_id').setOptions(menuExamine.map(s => ({label: s.name, value: s.id})));
        document.querySelector('#menu_id').reset();
        // nếu có menu trong danh sách thì thêm vào template
        if (menuExamine.length > 0) {
            // Set giá trị mới cho Virtual Select
            const menuSelect = document.querySelector('#menu_id');
            if (menuSelect) {
                menuSelect.setValue(menuExamine[0].id);
            }
            
            $('#tb_menu tbody').empty();
            $('#tb_menu').show();
            generateTableMenu(menuExamine[0].id);
        } else {
            $('#tb_menu tbody').empty();
            $('#tb_menu').hide();
        }
    } catch (error) {
        console.error('Error in deleteMenuExample:', error);
    }
}

// xóa phần từ object trong mảng bằng id
function removeItemArrayByIdObject(arr, id) {
    var j = 0;
    for (var i = 0, l = arr.length; i < l; i++) {
        if (arr[i].id !== id) {
            arr[j++] = arr[i];
        }
    }
    arr.length = j;
}

// ===================== ADMIN FUNCTIONS =====================

// Hàm thêm thực phẩm dành riêng cho admin
function addFoodToMenuAdmin() {
    try {
        const menuTimeSelect = document.querySelector('#menuTime_id');
        const foodSelect = document.querySelector('#food_name');
        
        if (!menuTimeSelect || !foodSelect) {
            toarstError('Không tìm thấy form elements!');
            return;
        }
        
        const selectedFoodOptions = foodSelect.getSelectedOptions();
        
        if (!selectedFoodOptions) {
            toarstError('Vui lòng chọn thực phẩm!');
            return;
        }
        
        // Kiểm tra chi tiết selectedFoodOptions[0]
        const selectedOption = selectedFoodOptions;
        if (!selectedOption) {
            toarstError('Dữ liệu thực phẩm được chọn không hợp lệ!');
            return;
        }
        
        if (!selectedOption.customData) {
            console.error('Missing customData in selected option:', selectedOption);
            toarstError('Thông tin dinh dưỡng của thực phẩm không có sẵn!');
            return;
        }
        
        const menuTime_id = menuTimeSelect.value;
        
        if (!menuTime_id) {
            toarstError('Bạn chưa chọn giờ ăn!');
            return;
        }
        
        // Kiểm tra và khởi tạo menuExamine nếu cần
        if (!window.menuExamine || window.menuExamine.length === 0) {
            createNewMenuExample();
            
            // Kiểm tra lại sau khi tạo
            if (!window.menuExamine || window.menuExamine.length === 0) {
                toarstError('Không thể khởi tạo thực đơn!');
                return;
            }
        }
        
        const foodData = selectedOption.customData;
        const weight = parseInt($('#weight_food').val()) || 0;
        
        if (weight <= 0) {
            toarstError('Vui lòng nhập khối lượng hợp lệ (> 0)!');
            return;
        }
        
        // Tìm menu time tương ứng trong menu hiện tại
        let targetMenuTime = null;
        for (let menuTime of window.menuExamine[0].detail) {
            if (menuTime_id == menuTime.id) {
                targetMenuTime = menuTime;
                break;
            }
        }
        
        if (!targetMenuTime) {
            console.error('Menu time not found:', menuTime_id);
            toarstError('Không tìm thấy giờ ăn được chọn!');
            return;
        }
        
        // Tạo ID mới cho thực phẩm
        let foodId = targetMenuTime.listFood && targetMenuTime.listFood.length > 0 
            ? targetMenuTime.listFood[targetMenuTime.listFood.length - 1].id + 1 
            : 1;
        
        // Tạo object thực phẩm với đầy đủ thông tin
        let food = {
            "id": foodId,
            "id_food": selectedOption.value,
            "name": selectedOption.label,
            "weight": weight,
            
            // Sao chép tất cả thông tin từ foodData
            ...foodData
        };
        
        // Tính toán lại các giá trị dựa trên khối lượng
        caculateFoodInfo(food, weight);
        
        // Thêm vào danh sách
        if (!targetMenuTime.listFood) {
            targetMenuTime.listFood = [];
        }
        targetMenuTime.listFood.push(food);
        
        // Tạo template HTML và thêm vào table
        let foodTemplate = addFoodTemplate(food, menuTime_id);
        
        // Kiểm tra và thêm vào table
        const tableBody = $('#tb_menu tbody');
        if (tableBody.length === 0) {
            console.error('Table body not found');
            toarstError('Không tìm thấy bảng thực đơn!');
            return;
        }
        
        // Hiển thị table nếu đang ẩn
        $('#tb_menu').show();
        
        // Tìm vị trí để insert row mới
        const menuTimeRow = $(`#menu_time_${menuTime_id}`);
        if (menuTimeRow.length === 0) {
            // Nếu không tìm thấy row menu time, rebuild lại toàn bộ table
            addTemplateListMenuTime(window.menuExamine[0].detail);
        } else {
            // Thêm row mới sau menu time row hoặc sau food cuối cùng
            const existingFoodRows = $(`tr[id^="food_${menuTime_id}_"]`);
            if (existingFoodRows.length > 0) {
                // Thêm sau food cuối cùng
                existingFoodRows.last().after(foodTemplate);
            } else {
                // Thêm ngay sau menu time row
                menuTimeRow.after(foodTemplate);
            }
            
            // Cập nhật rowspan cho menu time
            const totalFoodsInMenuTime = targetMenuTime.listFood ? targetMenuTime.listFood.length : 0;
            menuTimeRow.find('td:first-child').attr('rowspan', totalFoodsInMenuTime + 1);
        }
        
        // Tính lại tổng
        let listFoodTotal = [];
        for (let menuTime of window.menuExamine[0].detail) {
            if (menuTime.listFood && menuTime.listFood.length > 0) {
                listFoodTotal.push(...menuTime.listFood);
            }
        }
        setTotalMenu(listFoodTotal);
        
        // Reset form
        foodSelect.reset();
        $('#weight_food').val('');
        toarstMessage('Đã thêm thực phẩm vào thực đơn!');
    } catch (error) {
        console.error('Lỗi khi thêm thực phẩm:', error);
        toarstError('Có lỗi xảy ra khi thêm thực phẩm: ' + error.message);
    }
}

// Khởi tạo thực đơn admin
function initAdminMenuExample() {

    // Khởi tạo food selection
    generateFoodName("food_name");
    
    // Khởi tạo dish selection
    generateDishName("dish_name");
    
    // Khởi tạo thực đơn
    if (window.menuExamine && window.menuExamine.length > 0) {
        // Hiển thị bảng thực đơn nếu có dữ liệu
        $('#tb_menu').show();
        generateTableMenu(window.menuExamine[0].id);
        
        // Tính tổng thực phẩm
        let listFoodTotal = [];
        for (let menuTime of window.menuExamine[0].detail) {
            if (menuTime.listFood && menuTime.listFood.length > 0) {
                listFoodTotal.push(...menuTime.listFood);
            }
        }
        setTotalMenu(listFoodTotal);
    } else {
        // Nếu không có dữ liệu, tạo thực đơn trống
        createNewMenuExample();
    }
    
    // Setup event listeners
    setupMenuNameChange();
}

// Tạo thực đơn mẫu mới cho admin
function createNewMenuExample() {
    try {
        // Tạo cấu trúc thực đơn trống
        let menuDetail = [];
        if (window.listMenuTime && window.listMenuTime.length > 0) {
            menuDetail = window.listMenuTime.map(time => ({
                id: time.id,
                name: time.name,
                name_course: '',
                listFood: []
            }));
        }
        
        const newMenu = {
            id: 1,
            name: 'Thực đơn mới',
            detail: menuDetail,
            note: '',
            created_at: new Date().toISOString(), // Thêm ngày tạo
            created_by: (typeof window.user !== 'undefined' && window.user.id) ? window.user.id : null // Thêm người tạo nếu có
        };
        
        // Cập nhật global variable
        if (!window.menuExamine) {
            window.menuExamine = [];
        }
        window.menuExamine = [newMenu];
        
        // Hiển thị bảng
        $('#tb_menu').show();
        generateTableMenu(1);
    } catch (error) {
        console.error('Lỗi khi tạo thực đơn mới:', error);
    }
}

// Hàm hiển thị modal cập nhật tên thực đơn cho admin
function showUpdateMenuNameModal() {
    Swal.fire({
        title: 'Cập nhật tên thực đơn',
        input: 'text',
        inputValue: window.menuExamine && window.menuExamine.length > 0 ? window.menuExamine[0].name : '',
        inputPlaceholder: 'Nhập tên thực đơn mới',
        showCancelButton: true,
        confirmButtonText: 'Cập nhật',
        cancelButtonText: 'Hủy',
        inputValidator: (value) => {
            if (!value || value.trim() === '') {
                return 'Vui lòng nhập tên thực đơn!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            const newName = result.value.trim();
            $('#name_menu_text').val(newName);
            
            // Cập nhật menuExamine
            if (window.menuExamine && window.menuExamine.length > 0) {
                window.menuExamine[0].name = newName;
            }
            
            toarstMessage('Đã cập nhật tên thực đơn. Nhấn "Lưu" để hoàn tất.');
        }
    });
}

// Cập nhật tên thực đơn khi thay đổi input cho admin
function setupMenuNameChange() {
    $('#name_menu_text').on('input', function() {
        const newName = $(this).val();
        
        // Cập nhật menuExamine
        if (window.menuExamine && window.menuExamine.length > 0) {
            window.menuExamine[0].name = newName || 'Thực đơn mới';
        }
    });
}

// Functions để xử lý món ăn
function generateDishName(id) {
    try {
        // Tự động phát hiện URL phù hợp (admin vs client)
        const isAdminPage = window.location.pathname.includes('/admin/');
        const apiUrl = isAdminPage ? '/admin/api/dishes-for-select' : '/api/dishes-for-select';
        
        // Lấy danh sách món ăn từ server
        $.ajax({
            url: apiUrl,
            type: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    const options = response.data.map(dish => ({
                        label: dish.label,
                        value: dish.value,
                        customData: dish
                    }));
                    
                    VirtualSelect.init({
                        ele: '#' + id,
                        options: options,
                        optionsCount: 10,
                        placeholder: 'Chọn món ăn',
                        search: true,
                        searchPlaceholderText: 'Tìm kiếm món ăn...',
                        noSearchResultsText: 'Không tìm thấy món ăn nào',
                        noOptionsText: 'Không có món ăn nào'
                    });
                } else {
                    console.error('Không thể lấy danh sách món ăn:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Lỗi khi lấy danh sách món ăn:', error);
            }
        });
    } catch (error) {
        console.error('Error in generateDishName:', error);
    }
}

// Thêm món ăn vào thực đơn
function addDishToMenu() {
    try {
        
        const dishMenuTimeSelect = document.querySelector('#dish_menuTime_id');
        const dishSelect = document.querySelector('#dish_name');
        
        if (!dishMenuTimeSelect || !dishSelect) {
            console.error('Form elements not found:', { dishMenuTimeSelect, dishSelect });
            toarstError('Không tìm thấy form chọn món ăn!');
            return;
        }
        
        const menuTimeId = dishMenuTimeSelect.value;
        const selectedDishOptions = dishSelect.getSelectedOptions();
        
        if (!menuTimeId) {
            toarstError('Vui lòng chọn giờ ăn!');
            return;
        }
        
        if (!selectedDishOptions || selectedDishOptions.length === 0) {
            toarstError('Vui lòng chọn món ăn!');
            return;
        }
        
        const dishData = selectedDishOptions.customData;
        const dishId = selectedDishOptions.value;
        
        if (!dishData || !dishId) {
            console.error('Invalid dish data:', { dishData, dishId });
            toarstError('Dữ liệu món ăn không hợp lệ!');
            return;
        }
        
        // Tự động phát hiện URL phù hợp (admin vs client)
        const isAdminPage = window.location.pathname.includes('/admin/');
        const dishFoodsApiUrl = isAdminPage ? `/admin/api/dish-foods/${dishId}` : `/api/dish-foods/${dishId}`;
        
        // Lấy chi tiết thực phẩm trong món ăn
        $.ajax({
            url: dishFoodsApiUrl,
            type: 'GET',
            success: function(response) {
                if (response.success && response.data && response.data.length > 0) {
                    // Thêm từng thực phẩm trong món ăn vào thực đơn
                    addDishFoodsToMenu(response.data, menuTimeId, dishData.label);
                } else {
                    toarstError(response.message || 'Món ăn này chưa có thực phẩm nào!');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error when fetching dish foods:', { xhr, status, error });
                toarstError('Có lỗi xảy ra khi lấy thông tin món ăn!');
            }
        });
        
    } catch (error) {
        console.error('Error in addDishToMenu:', error);
        toarstError('Có lỗi xảy ra khi thêm món ăn!');
    }
}

// Thêm các thực phẩm từ món ăn vào thực đơn
function addDishFoodsToMenu(dishFoods, menuTimeId, dishName) {
    try {
        // Kiểm tra xem có phải trang admin không
        const isAdminPage = window.location.pathname.includes('/admin/thuc-don-mau/');
        let menuExamineData = isAdminPage ? window.menuExamine : menuExamine;
        
        if (!menuExamineData || menuExamineData.length === 0) {
            console.error('No menu examine data found');
            toarstError('Chưa có dữ liệu thực đơn!');
            return;
        }
        
        // Lấy menu hiện tại đang được chọn
        let currentMenu = null;
        
        if (isAdminPage) {
            // Trong trang admin, luôn dùng menu đầu tiên
            currentMenu = menuExamineData[0];
        } else {
            // Trong trang khau-phan-an, tìm menu theo ID được chọn
            const menuSelect = document.querySelector('#menu_id');
            const selectedMenuId = menuSelect ? menuSelect.value : null;
            
            if (selectedMenuId) {
                for (let menu of menuExamineData) {
                    if (menu.id == selectedMenuId) {
                        currentMenu = menu;
                        break;
                    }
                }
            }
            
            // Nếu không tìm thấy menu theo ID, dùng menu đầu tiên
            if (!currentMenu) {
                currentMenu = menuExamineData[0];
            }
        }
        
        if (!currentMenu) {
            console.error('Current menu not found');
            toarstError('Không tìm thấy thực đơn hiện tại!');
            return;
        }
        
        let listFoodTotal = [];
        let addedFoodsCount = 0;
        
        // Tìm menu time tương ứng
        let menuTimeFound = false;
        for (let menuTime of currentMenu.detail) {
            if (menuTimeId == menuTime.id) {
                menuTimeFound = true;
                
                // Cập nhật name_course với tên món ăn nếu chưa có hoặc để trống
                if (!menuTime.name_course || menuTime.name_course.trim() === '') {
                    menuTime.name_course = dishName;
                } else {
                    // Nếu đã có name_course, có thể thêm tên món ăn mới (tùy chọn)
                    if (!menuTime.name_course.includes(dishName)) {
                        menuTime.name_course += `, ${dishName}`;
                    }
                }
                
                // Thêm từng thực phẩm trong món ăn
                for (let dishFood of dishFoods) {
                    let foodId = menuTime.listFood.length === 0 ? 1 : menuTime.listFood[menuTime.listFood.length - 1].id + 1;
                    
                    // Tạo đối tượng food với đầy đủ thông tin như khi thêm từng thực phẩm
                    let food = {
                        "id": foodId,
                        "id_food": dishFood.food_id,
                        "name": dishFood.food_name + ` (${dishName})`,
                        "weight": dishFood.weight,
                        
                        // Các giá trị đã tính toán theo khối lượng thực tế (chính)
                        "energy": dishFood.calculated_energy || 0,
                        "protein": dishFood.calculated_protein || 0,
                        "animal_protein": dishFood.calculated_animal_protein || 0,
                        "lipid": dishFood.calculated_fat || 0,
                        "fat": dishFood.calculated_fat || 0,
                        "unanimal_lipid": dishFood.calculated_unanimal_lipid || 0,
                        "carbohydrate": dishFood.calculated_carbohydrate || 0,
                        
                        // Thông tin cơ bản từ food_info
                        "code": dishFood.food_code || '',
                        "ten": dishFood.food_ten || dishFood.food_name || '',
                        "type": dishFood.food_type || '',
                        "type_year": dishFood.food_type_year || '',
                        "edible": dishFood.food_edible || 100,
                        
                        // Các giá trị cơ sở (theo 100g) từ database để tham chiếu
                        "base_energy": dishFood.food_energy || 0,
                        "base_protein": dishFood.food_protein || 0,
                        "base_fat": dishFood.food_fat || 0,
                        "base_carbohydrate": dishFood.food_carbohydrate || 0,
                        "base_animal_protein": dishFood.food_animal_protein || 0,
                        "base_unanimal_lipid": dishFood.food_unanimal_lipid || 0,
                        
                        // Các chất dinh dưỡng khác đã tính toán
                        "water": dishFood.calculated_water || 0,
                        "fiber": dishFood.calculated_fiber || 0,
                        "ash": dishFood.calculated_ash || 0,
                        "calci": dishFood.calculated_calci || 0,
                        "phosphorous": dishFood.calculated_phosphorous || 0,
                        "fe": dishFood.calculated_fe || 0,
                        "zinc": dishFood.calculated_zinc || 0,
                        "sodium": dishFood.calculated_sodium || 0,
                        "potassium": dishFood.calculated_potassium || 0,
                        "magnesium": dishFood.calculated_magnesium || 0,
                        "manganese": dishFood.calculated_manganese || 0,
                        "copper": dishFood.calculated_copper || 0,
                        "selenium": dishFood.calculated_selenium || 0,
                        
                        // Vitamin đã tính toán
                        "vitamin_a_rae": dishFood.calculated_vitamin_a_rae || 0,
                        "vitamin_b1": dishFood.calculated_vitamin_b1 || 0,
                        "vitamin_b2": dishFood.calculated_vitamin_b2 || 0,
                        "vitamin_b6": dishFood.calculated_vitamin_b6 || 0,
                        "vitamin_b12": dishFood.calculated_vitamin_b12 || 0,
                        "vitamin_c": dishFood.calculated_vitamin_c || 0,
                        "vitamin_e": dishFood.calculated_vitamin_e || 0,
                        "vitamin_k": dishFood.calculated_vitamin_k || 0,
                        "folate": dishFood.calculated_folate || 0,
                        
                        // Axit béo đã tính toán
                        "total_fat": dishFood.calculated_total_fat || dishFood.calculated_fat || 0,
                        "total_saturated_fat": dishFood.calculated_total_saturated_fat || 0,
                        "mufa": dishFood.calculated_mufa || 0,
                        "fufa": dishFood.calculated_fufa || 0,
                        "cholesterol": dishFood.calculated_cholesterol || 0,
                        
                        // Đường đã tính toán
                        "total_sugar": dishFood.calculated_total_sugar || 0,
                        "glucose": dishFood.calculated_glucose || 0,
                        "fructose": dishFood.calculated_fructose || 0,
                        "sucrose": dishFood.calculated_sucrose || 0,
                        "lactose": dishFood.calculated_lactose || 0,
                        "maltose": dishFood.calculated_maltose || 0,
                        
                        // Amino acid đã tính toán
                        "lysin": dishFood.calculated_lysin || 0,
                        "methionin": dishFood.calculated_methionin || 0,
                        "tryptophan": dishFood.calculated_tryptophan || 0,
                        "phenylalanin": dishFood.calculated_phenylalanin || 0,
                        "threonin": dishFood.calculated_threonin || 0,
                        "isoleucine": dishFood.calculated_isoleucine || 0,
                        "leucine": dishFood.calculated_leucine || 0,
                        "valine": dishFood.calculated_valine || 0
                    };
                    
                    menuTime.listFood.push(food);
                    addedFoodsCount++;
                    foodId++;
                }
                break;
            }
        }
        
        if (!menuTimeFound) {
            console.error('Menu time not found:', menuTimeId);
            toarstError('Không tìm thấy giờ ăn được chọn!');
            return;
        }
        
        // Collect all foods for total calculation từ tất cả menu time
        for (let menuTime of currentMenu.detail) {
            if (menuTime.listFood && menuTime.listFood.length > 0) {
                listFoodTotal.push(...menuTime.listFood);
            }
        }
        
        // Hiển thị table nếu đang ẩn
        $('#tb_menu').show();
        
        // Rebuild lại toàn bộ table với dữ liệu mới
        addTemplateListMenuTime(currentMenu.detail);
        
        // Reset dish selection
        const dishSelect = document.querySelector('#dish_name');
        if (dishSelect && dishSelect.reset) {
            dishSelect.reset();
        }
        toarstMessage(`Đã thêm món "${dishName}" (${addedFoodsCount} thực phẩm) vào thực đơn!`);
        
    } catch (error) {
        console.error('Error in addDishFoodsToMenu:', error);
        toarstError('Có lỗi xảy ra khi thêm thực phẩm từ món ăn!');
    }
}

