var initializedFlags = {};
var loading = $("#loading-page");

function readyDom(fn) {
    if (document.readyState !== 'loading') {
        fn();
    } else {
        document.addEventListener('DOMContentLoaded', fn);
    }
}

readyDom(() =>{
    initVirtualSelect('[data-plugin="virtual-select"]');
    initFlatpickr('[data-plugin="flatpickr"]');
    expandTextarea();
})

function initVirtualSelect(query){
    // Khởi tạo virtual select
    const elVirtualSelect = document.querySelectorAll(query);
    Array.prototype.forEach.call(elVirtualSelect, (el) => {
        const id = el.id; // Lấy ID để làm khóa
        // Đánh dấu trạng thái khởi tạo là false
        initializedFlags[id] = false;
        let defaults = {
            ele: el,
            search: false,
            position: "auto"
        };
        // Set options
        let options = el.getAttribute('data-options');
        if(isJSON(options)) options = JSON.parse(options);
        else options = [];
        options = options && Array.isArray(options) && options.length > 0 ? options : [];
        if(options.length > 0) defaults['options'] = options;
        
        // Set Value
        let value = el.getAttribute('data-value');
        let configAttr = el.getAttribute('data-config');
        
        // Parse config attribute safely
        let parsedConfig = {};
        if(isJSON(configAttr)) {
            parsedConfig = JSON.parse(configAttr);
        }
        
        if(value){
            if(parsedConfig.multiple && isJSON(value)){
                defaults['selectedValue'] = JSON.parse(value);
            } else {
                defaults['selectedValue'] = value;
            }
        } 
        
        // Config - sử dụng parsedConfig thay vì parse lại
        let config = extendObject({}, defaults, parsedConfig);
        VirtualSelect.init(config);
    });
}
//'[data-plugin="flatpickr"]'
function initFlatpickr(query){
    const elFlatpickr = document.querySelectorAll(query);
    Array.prototype.forEach.call(elFlatpickr, (el) => {
        let defaults = {
            dateFormat: 'd/m/Y',
            conjunction: ' - ',
            wrap: true,
            animate: false,
            locale: 'vn',
            // altInput: true
        };
        if (el.dataset.parent) {
            defaults.appendTo = document.querySelector(el.dataset.parent);
        }
        let options = extendObject({}, defaults, JSON.parse(el.getAttribute('data-options')));
        flatpickr(el, options);
    });
}

const Toast = Swal.mixin({
    toast: true,
    position: "top-end",
    showConfirmButton: false,
    timerProgressBar: true,
    timer: 2500,
    didOpen: (toast) => {
      toast.onmouseenter = Swal.stopTimer;
      toast.onmouseleave = Swal.resumeTimer;
    }
});

function getDataUrl(name){
    let urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

function toarstMessage(message, title = '', timer = 2500){
    Toast.fire({
        icon: "success",
        title: title,
        text: message,
        timer: timer
    });
}

function toarstError(message, title = '', timer = 2500){
    Toast.fire({
        icon: "error",
        title: title,
        text: message,
        timer: timer
    });
}

function toarstWarning(message, title, timer = 2500){
    Toast.fire({
        icon: "warning",
        titleText: title,
        text: message,
        timer: timer
    });
}

function confirmDialog(title, message){
    return new Promise((resolve, reject) => {
        Swal.fire({
            title: title,
            text: message,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#4e73df",
            cancelButtonColor: "#e74a3b",
            confirmButtonText: "Đồng ý!"
        }).then((result) => {
            resolve(result)
        });
    })
}

function toarstInfo(message, title){
    Toast.fire({
        icon: "info",
        title: title,
        text: message
    });
}

function isObject(obj) {
    return obj === Object(obj);
}

function isJSON(str) {
    try {
        JSON.parse(str);
        return true;  // Nếu JSON.parse không ném lỗi, chuỗi hợp lệ
    } catch (e) {
        return false; // Nếu JSON.parse ném lỗi, chuỗi không hợp lệ
    }
}

function checkEmailString(email) {
    let reg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    return reg.test(email);
}

function ajax_call_error(jqXHR, exception){
    console.log('ajax_call_error', exception)
    var msg = '';
    if (jqXHR.status === 0) {
        msg = 'Mất kết nối mạng. Vui lòng kiểm tra kết nối và thử lại.';
    } else if (jqXHR.status == 404) {
        msg = 'Không tìm thấy trang được yêu cầu. [404]';
    } else if (jqXHR.status == 500) {
        msg = 'Lỗi máy chủ nội bộ [500].';
    } else if (exception === 'parsererror') {
        msg = 'Phân tích cú pháp JSON không thành công.';
    } else if (exception === 'timeout') {
        msg = 'Lỗi hết thời gian.';
    } else if (exception === 'abort') {
        msg = 'Yêu cầu Ajax đã bị hủy bỏ.';
    } else {
        msg = jqXHR.responseText;
    }
}

/* exported extend, ready */
function extendObject(out) {
    out = out || {};

    for (let i = 1; i < arguments.length; i++) {
        if (!arguments[i]) {
            continue;
        }

        for (let key in arguments[i]) {
            if (Object.prototype.hasOwnProperty.call(arguments[i], key)) {
                out[key] = arguments[i][key];
            }
        }
    }

    return out;
};

function isVietnamesePhoneNumberValid(number) {
    if(number && number.length > 0) number = number.trim();
    return /(^(\+84|0)[35789]\d{8}$)|(^(\+84|0)(2\d{1,2})\d{7}$)/.test(number);
}

function toggleErrorHtml(id, check, text, borderInput = true){
    if(text) document.getElementById(id + '_error').innerText = text;
    if(check){
        // Field đúng ẩn border text
        if (!document.getElementById(id + '_error').classList.contains('d-none')) document.getElementById(id + '_error').classList.add('d-none');
        if(borderInput && document.getElementById(id)){
            if (document.getElementById(id).classList.contains('border-danger')) document.getElementById(id).classList.remove('border-danger');
        }
    }else{
        // Field sai hiện border text
        if (document.getElementById(id + '_error').classList.contains('d-none')) document.getElementById(id + '_error').classList.remove('d-none');
        if(borderInput && document.getElementById(id)){
            if (!document.getElementById(id).classList.contains('border-danger')) document.getElementById(id).classList.add('border-danger');
        }
    }
}

function removeElement(arr, index) {
    if (index > -1 && index < arr.length) {
        arr[index] = arr[arr.length - 1];  // Thay thế phần tử cần xóa bằng phần tử cuối cùng
        arr.pop();  // Xóa phần tử cuối cùng
    }
    return arr;
}

function checkInputEmpty(evt, text, isBorder){
    toggleErrorHtml(evt.target.id, (evt.target.value && evt.target.value !== 'no_value') ? true : false, text, isBorder == 0 ? true : false);
}

function checkEmailInput(evt, text, isBorder){
    toggleErrorHtml(evt.target.id, checkEmailString(evt.target.value) ? true : false, text, isBorder == 0 ? true : false);
}

function checkPhoneInput(evt, text, isBorder){
    toggleErrorHtml(evt.target.id, isVietnamesePhoneNumberValid(evt.target.value) ? true : false, text, isBorder == 0 ? true : false);
}

function formatThreeDigits( integerStr ){
    var len = integerStr.length;
    var formatted = "";

    var breakpoint = (len-1) % 3; // after which index to place the dot

    for(i = 0; i < len; i++){
      formatted += integerStr.charAt(i);
      if(i % 3 === breakpoint){
        if(i < len-1) // don't add dot for last digit
          formatted += ".";
      }
    }

    return formatted;
}

function formatInputNumber(evt, decimal = false){
    let value = evt.target.value;
    let val =  parseInt(value.replace(/[^0-9]/g,''));
    if(decimal) val =  parseInt(value.replace(/[^0-9.]/g,''));
    if(isNaN(val)) val = '';
    let value2 = formatThreeDigits(String(val));
    evt.target.value = value2;
}

function formatInputFloat(evt){
    let value = evt.target.value;

    // Kiểm tra nếu giá trị không phải số hợp lệ
    if (!/^\d*\.?\d*$/.test(value)) {
        evt.target.value = value.slice(0, -1); // Xóa ký tự vừa nhập
    }
}

function convertStringNumber(text){
    if(text){
        let value = parseInt(text.replace(/[^0-9]/g,''));
        return value;
    }else{
        return 0
    }
}

function removeDuplicatesByKey(arr, key) {
    const uniqueArray = [];
    const duplicateArray = [];
    const valueSet = new Set();

    arr.forEach(obj => {
        const value = obj[key]; // Lấy giá trị của key trong object

        // Kiểm tra nếu 'value' đã tồn tại trong Set
        if (valueSet.has(value)) {
            // Nếu trùng, thêm vào mảng duplicateArray
            duplicateArray.push(obj);
        } else {
            // Nếu không trùng, thêm vào Set và uniqueArray
            valueSet.add(value);
            uniqueArray.push(obj);
        }
    });

    return { uniqueArray, duplicateArray };
}

function removeDuplicatesByIndex(arr, index) {
    const uniqueArray = [];
    const duplicateArray = [];
    const valueSet = new Set();

    arr.forEach(subArr => {
        const value = subArr[index]; // Lấy giá trị từ phần tử tại index

        // Kiểm tra nếu 'value' đã tồn tại trong Set
        if (valueSet.has(value)) {
            // Nếu trùng, thêm vào mảng duplicateArray
            duplicateArray.push(subArr);
        } else {
            // Nếu không trùng, thêm vào Set và uniqueArray
            valueSet.add(value);
            uniqueArray.push(subArr);
        }
    });

    return { uniqueArray, duplicateArray };
}

function changeOptionsVirtualSelect(el, id, options){
    if(options && Array.isArray(options)){
        if(el){
            el.setOptions(options, true);
        }else if(id){
            document.querySelector('#' + id).setOptions(options, true);
        }
    }
}

function escapeHtml(string) {
    if (string == null) {
      return '';
    }
    return String(string).replace(/[&<>"'`=\/]/g, function (s) {
      return entityMap[s];
    });
}

function calculateAge(selectedDate) {
    // Kiểm tra nếu ngày được cung cấp hợp lệ
    if (!selectedDate) {
        console.error("Ngày không hợp lệ!");
        return null;
    }

    // Chuyển đổi ngày sang moment
    const birthDate = moment(selectedDate);
    const today = moment(); // Ngày hiện tại

    // Kiểm tra nếu ngày sinh hợp lệ
    if (!birthDate.isValid()) {
        console.error("Ngày sinh không hợp lệ!");
        return null;
    }

    // Tính tuổi theo năm
    const ageInYears = today.diff(birthDate, 'years');
    
    // Nếu dưới 2 tuổi, tính theo tháng
    if (ageInYears < 2) {
        const ageInMonths = today.diff(birthDate, 'months');
        return `${ageInMonths} tháng`; // Trả về số tháng
    }

    return `${ageInYears} tuổi`; // Trả về số năm
}

function setFormValues(formId, data) {
    // Lấy form element
    const $form = $(`#${formId}`);
    
    // Duyệt qua tất cả các phần tử có attribute name trong form
    $form.find('[name]').each(function() {
        const $element = $(this);
        const name = $element.attr('name');
        const value = data[name] !== undefined ? data[name] : '';
        
        // Kiểm tra xem element có phải là VirtualSelect không
        if ($element.hasClass('vscomp-hidden-input')) {
            // Là hidden input của VirtualSelect
            const virtualSelect = $element.closest('.vscomp-ele')[0];
            if (virtualSelect && virtualSelect.setValue) {
                virtualSelect.setValue(value);
            }
        } else {
            // Xử lý các input thông thường
            const tagName = $element.prop('tagName').toLowerCase();
            const type = $element.attr('type')?.toLowerCase();
            
            if (tagName === 'input' || tagName === 'textarea') {
                if (type === 'checkbox' || type === 'radio') {
                    $element.prop('checked', value === true || value === 'true' || value === 1 || value === '1');
                } else {
                    $element.val(value);
                }
            } else if (tagName === 'select') {
                $element.val(value);
            }
        }
    });
}

// Lấy dữ liệu để gửi Ajax (nếu cần)
function getFormData(formId) {
    const $form = $(`#${formId}`);
    const data = {};
    
    $form.find('[name]').each(function() {
        const $element = $(this);
        const name = $element.attr('name');
        
        if ($element.hasClass('vscomp-hidden-input')) {
            const virtualSelect = $element.closest('.vscomp-ele')[0];
            data[name] = virtualSelect?.getSelectedOptions()?.[0]?.value || '';
        } else {
            const type = $element.attr('type')?.toLowerCase();
            if (type === 'checkbox' || type === 'radio') {
                data[name] = $element.prop('checked');
            } else {
                data[name] = $element.val();
            }
        }
    });
    
    return data;
}

function expandTextarea() {
    $('textarea').each(function () {
        if (this.scrollHeight !== 0) {
            this.setAttribute('style', 'height:' + (this.scrollHeight + 8) + 'px;overflow-y:hidden;');
        } else {
            this.setAttribute('style', 'height:auto;');
        }
    }).on('input', function () {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight + 8) + 'px';
    });
}

function createLinkList(path){
    let tag = document.createElement('a');
    tag.setAttribute('href', path);
    tag.innerHTML = 'Danh sách';
    document.querySelector('#sidebarToggleTop').insertAdjacentElement('afterend', tag);
}



// Hàm hiển thị thông tin chi tiết bệnh nhân trong modal
function displayPatientDetail(patient) {
    const genderText = patient.gender == 1 ? 'Nam' : (patient.gender == 0 ? 'Nữ' : 'Không xác định');
    const trinhDoOptions = ['Dưới THPT', 'THPT', 'Trung cấp/cao đẳng', 'Đại học/sau đại học'];
    const ngheNghiepOptions = ['Công nhân', 'Nông dân', 'Tự do', 'Viên chức', 'Khác'];
    const xepLoaiKTOptions = ['Nghèo', 'Cận nghèo', 'Không xếp loại/Không biết'];
    const danTocOptions = ['Kinh', 'Khác'];

    const formatDate = (dateString) => {
        if (!dateString || dateString === '0000-00-00') return 'Chưa cập nhật';
        return moment(dateString).format('DD/MM/YYYY');
    };

    const formatDateTime = (dateString) => {
        if (!dateString || dateString === '0000-00-00 00:00:00') return 'Chưa cập nhật';
        return moment(dateString).format('DD/MM/YYYY HH:mm');
    };
    const age = calculateAge(patient.birthday);

    let html = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Thông tin cơ bản</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Họ tên:</strong></td>
                                <td>${patient.fullname || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Số điện thoại:</strong></td>
                                <td>${patient.phone || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Giới tính:</strong></td>
                                <td>${genderText}</td>
                            </tr>
                            <tr>
                                <td><strong>Ngày sinh:</strong></td>
                                <td>${formatDate(patient.birthday)} (${age})</td>
                            </tr>
                            <tr>
                                <td><strong>Mã bệnh án:</strong></td>
                                <td>${patient.ma_benh_an || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Ngày nhập viện:</strong></td>
                                <td>${formatDate(patient.ngay_nhap_vien)}</td>
                            </tr>
                            <tr>
                                <td><strong>Phòng điều trị:</strong></td>
                                <td>${patient.phong_dieu_tri || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Dân tộc:</strong></td>
                                <td>${patient.dan_toc ? danTocOptions[parseInt(patient.dan_toc) + 1] || 'Khác' : 'N/A'}${patient.dan_toc_khac ? ' (' + patient.dan_toc_khac + ')' : ''}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">Thông tin bổ sung</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Trình độ học vấn:</strong></td>
                                <td>${patient.trinh_do ? trinhDoOptions[parseInt(patient.trinh_do) + 1] || 'N/A' : 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Nghề nghiệp:</strong></td>
                                <td>${patient.nghe_nghiep ? ngheNghiepOptions[ parseInt(patient.nghe_nghiep) + 1] || 'Khác' : 'N/A'}${patient.nghe_nghiep_khac ? ' (' + patient.nghe_nghiep_khac + ')' : ''}</td>
                            </tr>
                            <tr>
                                <td><strong>Nơi ở:</strong></td>
                                <td>${patient.noi_o || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Quê quán:</strong></td>
                                <td>${patient.que_quan || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Xếp loại kinh tế:</strong></td>
                                <td>${patient.xep_loai_kt ? xepLoaiKTOptions[parseInt(patient.xep_loai_kt) + 1] || 'N/A' : 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Khoa:</strong></td>
                                <td>${patient.khoa || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Cân nặng:</strong></td>
                                <td>${patient.cn || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Chiều cao:</strong></td>
                                <td>${patient.cc || 'N/A'}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">Thông tin y tế</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Chẩn đoán:</strong>
                                <p>${patient.chuan_doan || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <strong>Tiền sử bệnh:</strong>
                                <p>${patient.tien_su_benh || 'N/A'}</p>
                            </div>
                        </div>
                        ${patient.dieu_tra_vien ? `
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Điều tra viên:</strong>
                                <p>${patient.dieu_tra_vien}</p>
                            </div>
                            <div class="col-md-6">
                                <strong>Ngày hội chẩn:</strong>
                                <p>${formatDateTime(patient.ngay_hoi_chan)}</p>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">Trạng thái</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <span class="badge ${patient.active == 2 ? 'bg-danger' : 'bg-success'}">
                                    ${patient.active == 2 ? 'Đã ra viện' : 'Đang điều trị'}
                                </span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge ${patient.bien_ban == 1 ? 'bg-primary' : 'bg-secondary'}">
                                    ${patient.bien_ban == 1 ? 'Có biên bản' : 'Chưa có biên bản'}
                                </span>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">Tạo lúc: ${formatDateTime(patient.created_at)}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#patientDetailContent').html(html);
}

// Helper parse cho Virtual Select (support string/array)
function parseSelectedValues(val){
    if(val == null) return [];
    if(Array.isArray(val)) return val.map(v => parseInt(v, 10)).filter(v => !Number.isNaN(v));
    if(typeof val === 'string'){
        const trimmed = val.trim();
        if(trimmed.length === 0) return [];
        return trimmed.split(',').map(s => parseInt(s.trim(), 10)).filter(v => !Number.isNaN(v));
    }
    return [];
}

function parseNumber(str) {
    if(typeof str !== 'string') return str;
    // Giữ lại số và dấu .
    let cleaned = str.replace(/[^0-9.]/g, "");
  
    if (cleaned === "") return null; // không có số thì trả về null
  
    // Nếu là số nguyên
    if (/^\d+$/.test(cleaned)) {
      return parseInt(cleaned, 10);
    }
  
    // Nếu có dấu thập phân
    return parseFloat(cleaned);
  }