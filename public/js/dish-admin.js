// JavaScript cho quản lý món ăn
// Biến global để lưu danh sách thực phẩm trong món ăn
let dishFoods = [];
let foodSelectInstance = null;

$(document).ready(function() {
    // Khởi tạo Virtual Select cho thực phẩm
    initFoodSelect();
    
    // Load dữ liệu hiện tại nếu đang edit
    if (window.dishData && window.dishData.isEdit && window.dishData.existingFoods.length > 0) {
        dishFoods = window.dishData.existingFoods.map(food => ({
            food_id: food.food_id,
            food_name: food.food_name,
            food_code: food.food_code,
            food_type: food.food_type,
            food_type_year: food.food_type_year,
            weight: parseFloat(food.weight),
            food_energy: parseFloat(food.food_energy) || 0,
            food_protein: parseFloat(food.food_protein) || 0,
            food_fat: parseFloat(food.food_fat) || 0,
            calculated_energy: parseFloat(food.calculated_energy) || 0,
            calculated_protein: parseFloat(food.calculated_protein) || 0,
            calculated_fat: parseFloat(food.calculated_fat) || 0
        }));
        updateFoodTable();
    }

    // Event listeners cho filter
    $('#foodType, #foodYear').on('change', function() {
        updateFoodSelect();
    });
});

function initFoodSelect() {
    try {
        // Kiểm tra element tồn tại
        const selectElement = document.querySelector('#foodSelect');
        if (!selectElement) {
            console.error('Element #foodSelect không tồn tại!');
            return;
        }

        // Khởi tạo Virtual Select với API search
        VirtualSelect.init({
            ele: '#foodSelect',
            placeholder: 'Tìm kiếm thực phẩm...',
            search: true,
            searchPlaceholderText: 'Nhập tên thực phẩm (tối thiểu 2 ký tự)...',
            noSearchResultsText: 'Không tìm thấy thực phẩm nào',
            noOptionsText: 'Nhập từ khóa để tìm kiếm',
            optionsCount: 10,
            onServerSearch: function(search, virtualSelect) {
                
                if (search.length < 2) {
                    virtualSelect.setServerOptions([]);
                    return;
                }

                const type = $('#foodType').val();
                const type_year = $('#foodYear').val();
                $.ajax({
                    url: '/api/food-search',
                    type: 'GET',
                    data: {
                        search: search,
                        type: type,
                        type_year: type_year
                    },
                    success: function(response) {
                        
                        if (response.success && response.data) {
                            const options = response.data;
                            virtualSelect.setServerOptions(options);
                        } else {
                            virtualSelect.setServerOptions([]);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('API error:', error);
                        virtualSelect.setServerOptions([]);
                    }
                });
            }
        });
        
        foodSelectInstance = document.querySelector('#foodSelect');
        
    } catch (error) {
        console.error('Error initializing Virtual Select:', error);
    }
}

function updateFoodSelect() {
    // Reset search khi thay đổi filter
    if (foodSelectInstance) {
        foodSelectInstance.reset();
    }
}

function addFoodToDish() {
    try {
        // Kiểm tra foodSelectInstance tồn tại
        if (!foodSelectInstance) {
            toarstError('Lỗi hệ thống: Virtual Select chưa được khởi tạo!');
            return;
        }

        // Lấy thông tin đã chọn
        const selectedOptions = foodSelectInstance.getSelectedOptions();
        const weightInput = $('#foodWeight').val();

        // Kiểm tra đã chọn thực phẩm chưa
        if (!selectedOptions || selectedOptions.length === 0) {
            toarstError('Vui lòng chọn thực phẩm!');
            $('#foodSelect').focus();
            return;
        }

        // Kiểm tra khối lượng
        if (!weightInput || weightInput.trim() === '') {
            toarstError('Vui lòng nhập khối lượng!');
            $('#foodWeight').focus();
            return;
        }

        const weight = parseFloat(weightInput);
        if (isNaN(weight) || weight <= 0) {
            toarstError('Khối lượng phải là số dương! Ví dụ: 100, 150.5');
            $('#foodWeight').focus();
            return;
        }

        // Kiểm tra dữ liệu thực phẩm
        const foodData = selectedOptions.customData;
        if (!foodData || !foodData.id) {
            toarstError('Dữ liệu thực phẩm không hợp lệ!');
            return;
        }
        
        // Kiểm tra trùng lặp
        const existingIndex = dishFoods.findIndex(f => f.food_id == foodData.id);
        if (existingIndex !== -1) {
            toarstWarning('Thực phẩm "' + foodData.name + '" đã có trong danh sách!');
            return;
        }

        // Tính toán dinh dưỡng
        const ratio = weight / 100;
        const calculatedEnergy = (parseFloat(foodData.energy) || 0) * ratio;
        const calculatedProtein = (parseFloat(foodData.protein) || 0) * ratio;
        const calculatedFat = (parseFloat(foodData.fat) || 0) * ratio;

        // Thêm vào danh sách
        const newFood = {
            food_id: foodData.id,
            food_name: foodData.name || 'Không rõ tên',
            food_code: foodData.code || '',
            food_type: foodData.type || 'raw',
            food_type_year: foodData.type_year || '2017',
            weight: weight,
            food_energy: parseFloat(foodData.energy) || 0,
            food_protein: parseFloat(foodData.protein) || 0,
            food_fat: parseFloat(foodData.fat) || 0,
            calculated_energy: calculatedEnergy,
            calculated_protein: calculatedProtein,
            calculated_fat: calculatedFat
        };
        dishFoods.push(newFood);

        // Cập nhật bảng
        updateFoodTable();

        // Reset form
        foodSelectInstance.reset();
        $('#foodWeight').val('');
        
        toarstMessage('Đã thêm "' + foodData.name + '" vào món ăn!');
    } catch (error) {
        console.error('Error in addFoodToDish:', error);
        toarstError('Có lỗi xảy ra khi thêm thực phẩm!');
    }
}

function removeFoodFromDish(index) {
    dishFoods.splice(index, 1);
    updateFoodTable();
    toarstMessage('Đã xóa thực phẩm khỏi món ăn!');
}

function updateFoodTable() {
    const tbody = $('#dishFoodsBody');
    const emptyMessage = $('#emptyMessage');
    
    if (dishFoods.length === 0) {
        tbody.empty();
        emptyMessage.show();
        $('#totalWeight').text('0');
        $('#totalEnergy').text('0');
        return;
    }

    emptyMessage.hide();
    
    let html = '';
    let totalWeight = 0;
    let totalEnergy = 0;

    dishFoods.forEach((food, index) => {
        totalWeight += food.weight;
        totalEnergy += food.calculated_energy;
        
        html += '<tr>';
        html += '<td>' + (index + 1) + '</td>';
        html += '<td>' + food.food_name + '</td>';
        html += '<td>' + (food.food_code || '') + '</td>';
        html += '<td><span class="food-type-badge ' + (food.food_type === 'raw' ? 'food-type-raw' : 'food-type-cooked') + '">' + textTypeFood(food.food_type) + ' (' + food.food_type_year + ')</span></td>';
        html += '<td class="nutrition-value">' + food.weight.toFixed(1) + '</td>';
        html += '<td class="nutrition-value">' + food.calculated_energy.toFixed(1) + '</td>';
        html += '<td class="nutrition-value">' + food.calculated_protein.toFixed(1) + '</td>';
        html += '<td class="nutrition-value">' + food.calculated_fat.toFixed(1) + '</td>';
        html += '<td><button class="btn btn-sm btn-danger" onclick="removeFoodFromDish(' + index + ')" title="Xóa"><i class="fas fa-trash"></i></button></td>';
        html += '</tr>';
    });

    tbody.html(html);
    $('#totalWeight').text(totalWeight.toFixed(1));
    $('#totalEnergy').text(totalEnergy.toFixed(1));
}

function textTypeFood(type) {
    switch(type) {
        case 'raw':
            return 'Sống';
        case 'cooked':
            return 'Chín ĐP';
        case 'cooked_vdd':
            return 'Chín VDD';
        case 'milk':
            return 'Sữa';
        case 'ddd':
            return 'Dịch DD';
        default:
            return '';
    }
}

function saveDish() {
    try {
        // Validate
        const dishName = $('#dishName').val();
        const dishCategory = $('#dishCategory').val();
        const dishDescription = $('#dishDescription').val();
        const dishId = $('#dishId').val();
        
        if (!dishName || dishName.trim() === '') {
            toarstError('Vui lòng nhập tên món ăn!');
            $('#dishName').focus();
            return;
        }

        if (dishFoods.length === 0) {
            toarstError('Vui lòng thêm ít nhất một thực phẩm vào món ăn!');
            return;
        }

        // Chuẩn bị dữ liệu gửi lên server
        const dataToSend = {
            name: dishName.trim(),
            category: dishCategory || '',
            description: dishDescription || '',
            dish_foods: JSON.stringify(dishFoods)
        };
        
        // Thêm ID nếu đang edit
        if (dishId) {
            dataToSend.id = dishId;
        }

        // Disable button
        const saveBtn = event.target;
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang lưu...';

        $.ajax({
            url: '/admin/mon-an/upsert',
            type: 'POST',
            data: dataToSend,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toarstMessage(response.message);
                    setTimeout(() => {
                        window.location.href = '/admin/mon-an';
                    }, 1500);
                } else {
                    toarstError(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Save error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                toarstError('Có lỗi xảy ra khi lưu món ăn!');
            },
            complete: function() {
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
            }
        });
    } catch (error) {
        console.error('Error in saveDish:', error);
        toarstError('Có lỗi xảy ra trong quá trình xử lý!');
    }
}