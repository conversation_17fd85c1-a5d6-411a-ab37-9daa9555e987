<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Radio Button with Input</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <h3>Chọn một tùy chọn:</h3>
        <form>
            <!-- Radio Buttons -->
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="option" id="optionKinh" value="kinh" checked>
                    <label class="form-check-label" for="optionKinh">
                        Kinh
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="option" id="optionKhac" value="khac">
                    <label class="form-check-label" for="optionKhac">
                        Khác
                    </label>
                </div>
            </div>

            <!-- Input Field (Ẩn mặc định) -->
            <div class="mb-3 d-none" id="otherInputContainer">
                <label for="otherInput" class="form-label">Nhập nội dung khác:</label>
                <input type="text" class="form-control" id="otherInput" placeholder="Nhập nội dung...">
            </div>
        </form>
    </div>

    <!-- Bootstrap JS + Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Script -->
    <script>
        // Lấy các radio button và div input
        const optionKinh = document.getElementById("optionKinh");
        const optionKhac = document.getElementById("optionKhac");
        const otherInputContainer = document.getElementById("otherInputContainer");

        // Sự kiện khi chọn radio button "Kinh"
        optionKinh.addEventListener("change", function () {
            if (this.checked) {
                otherInputContainer.classList.add("d-none"); // Ẩn input
            }
        });

        // Sự kiện khi chọn radio button "Khác"
        optionKhac.addEventListener("change", function () {
            if (this.checked) {
                otherInputContainer.classList.remove("d-none"); // Hiện input
            }
        });
    </script>
</body>
</html>
