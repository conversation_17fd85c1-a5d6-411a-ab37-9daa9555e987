const commonService = require('../services/commonService');
const securityService = require('../services/securityService');
const emailService = require('../services/emailService');
const webhookService = require('../services/webhookService');
const automationService = require('../services/automationService');

const automationController = {
    /**
     * Get automation rules
     */
    getAutomationRules: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            
            // Check project access
            const projectResponse = await commonService.getAllDataTable('projects', { id: projectId });
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.status(404).json({ success: false, message: 'Project not found' });
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.status(403).json({ success: false, message: 'Access denied' });
            }
            
            // Get automation rules
            const rulesResponse = await commonService.executeQuery(`
                SELECT ar.*, u.username as created_by_name
                FROM automation_rules ar
                LEFT JOIN users u ON ar.created_by = u.id
                WHERE ar.created_by = ? OR ? = 'admin'
                ORDER BY ar.created_at DESC
            `, [user.id, user.role]);
            
            if (rulesResponse.success) {
                const rules = rulesResponse.data.map(rule => ({
                    ...rule,
                    rule_config: JSON.parse(rule.rule_config)
                }));
                
                res.json({
                    success: true,
                    data: rules
                });
            } else {
                res.status(500).json({ success: false, message: 'Failed to get automation rules' });
            }
            
        } catch (error) {
            console.error('Error in getAutomationRules:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Create automation rule
     */
    createAutomationRule: async (req, res) => {
        try {
            const user = req.user;
            const { name, description, config, active } = req.body;
            
            if (!name || !config) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Name and config are required' 
                });
            }
            
            const result = await automationService.createAutomationRule({
                name,
                description,
                config,
                active,
                created_by: user.id
            });
            
            if (result.success) {
                res.json({
                    success: true,
                    message: 'Automation rule created successfully',
                    data: { id: result.insertId }
                });
            } else {
                res.status(500).json({ 
                    success: false, 
                    message: result.error || 'Failed to create automation rule' 
                });
            }
            
        } catch (error) {
            console.error('Error in createAutomationRule:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Update automation rule
     */
    updateAutomationRule: async (req, res) => {
        try {
            const user = req.user;
            const ruleId = req.params.ruleId;
            const updateData = req.body;
            
            // Check if user can update this rule
            const ruleResponse = await commonService.getAllDataTable('automation_rules', { id: ruleId });
            if (!ruleResponse.success || !ruleResponse.data || ruleResponse.data.length === 0) {
                return res.status(404).json({ success: false, message: 'Automation rule not found' });
            }
            
            const rule = ruleResponse.data[0];
            if (!securityService.canAccessRecord(user, rule)) {
                return res.status(403).json({ success: false, message: 'Access denied' });
            }
            
            const result = await automationService.updateAutomationRule(ruleId, updateData);
            
            if (result.success) {
                res.json({
                    success: true,
                    message: 'Automation rule updated successfully'
                });
            } else {
                res.status(500).json({ 
                    success: false, 
                    message: result.error || 'Failed to update automation rule' 
                });
            }
            
        } catch (error) {
            console.error('Error in updateAutomationRule:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Delete automation rule
     */
    deleteAutomationRule: async (req, res) => {
        try {
            const user = req.user;
            const ruleId = req.params.ruleId;
            
            // Check if user can delete this rule
            const ruleResponse = await commonService.getAllDataTable('automation_rules', { id: ruleId });
            if (!ruleResponse.success || !ruleResponse.data || ruleResponse.data.length === 0) {
                return res.status(404).json({ success: false, message: 'Automation rule not found' });
            }
            
            const rule = ruleResponse.data[0];
            if (!securityService.canAccessRecord(user, rule)) {
                return res.status(403).json({ success: false, message: 'Access denied' });
            }
            
            const result = await automationService.deleteAutomationRule(ruleId);
            
            if (result.success) {
                res.json({
                    success: true,
                    message: 'Automation rule deleted successfully'
                });
            } else {
                res.status(500).json({ 
                    success: false, 
                    message: result.error || 'Failed to delete automation rule' 
                });
            }
            
        } catch (error) {
            console.error('Error in deleteAutomationRule:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Test webhook
     */
    testWebhook: async (req, res) => {
        try {
            const { url, secret, headers } = req.body;
            
            if (!url) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Webhook URL is required' 
                });
            }
            
            const webhookConfig = {
                url,
                secret,
                headers: headers || {}
            };
            
            const result = await webhookService.testWebhook(webhookConfig);
            
            res.json({
                success: result.success,
                message: result.success ? 'Webhook test successful' : 'Webhook test failed',
                data: result
            });
            
        } catch (error) {
            console.error('Error in testWebhook:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Send test email
     */
    testEmail: async (req, res) => {
        try {
            const { to, template, data } = req.body;
            
            if (!to) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Recipient email is required' 
                });
            }
            
            let result;
            
            switch (template) {
                case 'invitation':
                    result = await emailService.sendSurveyInvitation({
                        to,
                        surveyName: data?.surveyName || 'Test Survey',
                        surveyUrl: data?.surveyUrl || 'https://example.com/survey/test',
                        senderName: data?.senderName || 'Test Sender',
                        customMessage: 'This is a test email from the survey system.'
                    });
                    break;
                    
                case 'reminder':
                    result = await emailService.sendSurveyReminder({
                        to,
                        surveyName: data?.surveyName || 'Test Survey',
                        surveyUrl: data?.surveyUrl || 'https://example.com/survey/test',
                        reminderNumber: 1
                    });
                    break;
                    
                case 'admin':
                    result = await emailService.sendAdminNotification({
                        to,
                        subject: 'Test Admin Notification',
                        message: 'This is a test admin notification from the survey system.',
                        priority: 'normal'
                    });
                    break;
                    
                default:
                    return res.status(400).json({ 
                        success: false, 
                        message: 'Invalid email template' 
                    });
            }
            
            res.json({
                success: result.success,
                message: result.success ? 'Test email sent successfully' : 'Failed to send test email',
                data: result
            });
            
        } catch (error) {
            console.error('Error in testEmail:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Get webhook logs
     */
    getWebhookLogs: async (req, res) => {
        try {
            const { url, success, start_date, end_date, limit } = req.query;
            
            const filters = {
                webhook_url: url,
                success: success !== undefined ? success === 'true' : undefined,
                start_date,
                end_date,
                limit: parseInt(limit) || 100
            };
            
            const result = await webhookService.getWebhookLogs(filters);
            
            if (result.success) {
                res.json({
                    success: true,
                    data: result.data
                });
            } else {
                res.status(500).json({ 
                    success: false, 
                    message: result.error || 'Failed to get webhook logs' 
                });
            }
            
        } catch (error) {
            console.error('Error in getWebhookLogs:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Get webhook statistics
     */
    getWebhookStats: async (req, res) => {
        try {
            const { url, days } = req.query;
            
            if (!url) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Webhook URL is required' 
                });
            }
            
            const result = await webhookService.getWebhookStats(url, parseInt(days) || 7);
            
            if (result.success) {
                res.json({
                    success: true,
                    data: result.stats
                });
            } else {
                res.status(500).json({ 
                    success: false, 
                    message: result.error || 'Failed to get webhook statistics' 
                });
            }
            
        } catch (error) {
            console.error('Error in getWebhookStats:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Send survey invitations
     */
    sendSurveyInvitations: async (req, res) => {
        try {
            const user = req.user;
            const surveyConfigId = req.params.surveyConfigId;
            const { emails, customMessage } = req.body;
            
            if (!emails || !Array.isArray(emails) || emails.length === 0) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Email list is required' 
                });
            }
            
            // Get survey config
            const surveyResponse = await commonService.getAllDataTable('survey_configs', { id: surveyConfigId });
            if (!surveyResponse.success || !surveyResponse.data || surveyResponse.data.length === 0) {
                return res.status(404).json({ success: false, message: 'Survey not found' });
            }
            
            const survey = surveyResponse.data[0];
            if (!securityService.canAccessRecord(user, survey)) {
                return res.status(403).json({ success: false, message: 'Access denied' });
            }
            
            const results = [];
            
            for (const email of emails) {
                try {
                    // Create invitation record
                    const invitation = {
                        id: commonService.generateUUID(),
                        survey_config_id: surveyConfigId,
                        email: email,
                        invitation_token: commonService.generateUUID(),
                        created_by: user.id,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    
                    await commonService.insertRecordTable(invitation, 'survey_invitations');
                    
                    // Send invitation email
                    const emailResult = await emailService.sendSurveyInvitation({
                        to: email,
                        surveyName: survey.name,
                        surveyUrl: `${process.env.BASE_URL || 'http://localhost:3000'}/survey/${survey.survey_url_slug}`,
                        senderName: user.username,
                        customMessage: customMessage,
                        expiryDate: survey.expiry_date
                    });
                    
                    results.push({
                        email: email,
                        success: emailResult.success,
                        error: emailResult.error || null
                    });
                    
                } catch (error) {
                    results.push({
                        email: email,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            const successCount = results.filter(r => r.success).length;
            
            res.json({
                success: true,
                message: `${successCount} of ${emails.length} invitations sent successfully`,
                data: results
            });
            
        } catch (error) {
            console.error('Error in sendSurveyInvitations:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    },
    
    /**
     * Get survey invitations
     */
    getSurveyInvitations: async (req, res) => {
        try {
            const user = req.user;
            const surveyConfigId = req.params.surveyConfigId;
            
            // Check survey access
            const surveyResponse = await commonService.getAllDataTable('survey_configs', { id: surveyConfigId });
            if (!surveyResponse.success || !surveyResponse.data || surveyResponse.data.length === 0) {
                return res.status(404).json({ success: false, message: 'Survey not found' });
            }
            
            const survey = surveyResponse.data[0];
            if (!securityService.canAccessRecord(user, survey)) {
                return res.status(403).json({ success: false, message: 'Access denied' });
            }
            
            // Get invitations with response status
            const invitationsResponse = await commonService.executeQuery(`
                SELECT 
                    si.*,
                    CASE WHEN sr.id IS NOT NULL THEN 1 ELSE 0 END as has_responded,
                    sr.submitted_at as response_date
                FROM survey_invitations si
                LEFT JOIN survey_responses sr ON si.email = sr.respondent_email 
                    AND sr.survey_config_id = si.survey_config_id
                WHERE si.survey_config_id = ?
                ORDER BY si.sent_at DESC
            `, [surveyConfigId]);
            
            if (invitationsResponse.success) {
                res.json({
                    success: true,
                    data: invitationsResponse.data
                });
            } else {
                res.status(500).json({ success: false, message: 'Failed to get invitations' });
            }
            
        } catch (error) {
            console.error('Error in getSurveyInvitations:', error.message);
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    }
};

module.exports = automationController;
