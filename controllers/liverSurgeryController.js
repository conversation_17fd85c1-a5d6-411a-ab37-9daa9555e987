var moment          = require('moment'),
    commonService   = require('../services/commonService'),
    securityService = require('../services/securityService');
const dataTableService = require('../services/dataTableService');

let liverSurgery = {
    index: function(req, res){
        try {
            const arrPromise = [];
            const errors = [];
            const patient_id = req.params.patient_id;
            const user = req.user;
            let patient = {};
            let detailLiverSurgery = {};
            let typeDetail = {view: 'index'};
            const type = req.params.type;
            if(user.isAdmin || user.role_id.includes(5)){
                // lấy thông tin cơ bản
                arrPromise.push(
                    commonService.getAllDataTable('patients', securityService.applyRoleBasedFiltering(req.user, {id: patient_id})).then(responseData =>{
                        if(responseData.success){
                            if(responseData.data && responseData.data.length > 0){
                                patient = responseData.data[0];
                            }
                        }else{
                            errors.push(responseData.message);  
                        }
                    })
                )
            }else{
                errors.push('<PERSON>ạ<PERSON> không có quyền truy cập bệnh nhân này!');
            }

            Promise.all(arrPromise).then(responseData =>{
                return res.render('cat-gan-nho/' + typeDetail.view, {
                    user: req.user,
                    errors: errors,
                    patient: patient,
                    moment: moment,
                    detailLiverSurgery: detailLiverSurgery,
                    type: type,
                    path: 'hoi-chan'
                });
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            return res.render("error");
        }
    },
    addBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                insertId: ''
            };
            const validateRules = [
                // { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" }
            ];
            const parameter = liverSurgery.getDataBodyBroading(req.body, req.params.type);
            const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
            if(!req.user.role_id.includes(5) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền tạo danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                parameter.data['created_by'] = req.user.id;
                parameter.data['patient_id'] = req.params.patient_id;
                parameter.data['campaign_id'] = req.user.campaign_id;
                parameter.data.time = parameter.data.time.split("/").reverse().join("/");
                commonService.addRecordTable(parameter.data, parameter.table, true).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                        resultData.insertId = responseData.data.insertId;
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    updateBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: ""
            };
            const validateRules = [
                // { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" }
            ];
            const parameter = liverSurgery.getDataBodyBroading(req.body, req.params.type);
            const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
            let id = req.body.id;
            if(!id){
                errors.push('Thiếu Id');
            }
            if(!req.user.role_id.includes(5) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền sửa danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                parameter.data.time = parameter.data.time.split("/").reverse().join("/");
                commonService.updateRecordTable(parameter.data, {id: req.body.id}, parameter.table).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    dataBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                data: {}
            };
            let table = 'cat_gan_nho_kpa';
            if(!req.user.role_id.includes(5) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền truy cập danh sách này!';
                return res.json(resultMessage);
            }
            commonService.getAllDataTable(table, securityService.applyRoleBasedFiltering(req.user, {id: req.params.id})).then(responseData =>{
                if(responseData.success){
                    resultData.success = true;
                    if(responseData.data && responseData.data.length > 0){
                        resultData.data = responseData.data[0];
                    }else{
                        resultData.message = 'Không có dữ liệu';
                    }
                }else{
                    resultData.message = responseData.message;
                }
                return res.json(resultData);
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    deleteBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: ""
            };
            let id = req.params.id;
            let table = 'cat_gan_nho_kpa';
            if(!req.user.role_id.includes(5) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có xóa danh sách này!';
                return res.json(resultMessage);
            }
            if(id){
                commonService.updateRecordTable({active: 0}, securityService.applyRoleBasedFiltering(req.user, {id: id}), table).then(responseData =>{
                    if(responseData.success){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                    }else{
                        resultData.message = responseData.message;
                    }
                    return res.json(resultData);
                })
            }else{
                resultData.message = 'Thiếu Id bệnh nhân!';
                return res.json(resultData);
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    getDataBodyBroading: function(body, type){
        return {data: liverSurgery.khauPhanAn(body), table: 'cat_gan_nho_kpa'}
    },
    khauPhanAn: function(body){
        return {
            time: body.date,
            nd_duong_th: body.nd_duong_th,
            nd_tinh_mac: body.nd_tinh_mac,
            note: body.note,
            xet_nghiem: body.xet_nghiem,
            y_kien_bs: body.y_kien_bs
        }
    },
    getListTable: function(req, res, next){
        // Kiểm tra quyền truy cập
        if (!req.user.isAdmin && !req.user.role_id.includes(5)) {
            return res.json(dataTableService.createErrorResponse(req.body, 'Bạn không có quyền truy cập danh sách này!'));
        }

        // Cấu hình DataTable
        const config = {
            table: 'cat_gan_nho_kpa',
            primaryKey: 'id',
            active: 0,
            activeOperator: '!=',
            filters: {
                patient_id: req.params.patient_id
            },
            searchColumns: ['nd_duong_th', 'nd_tinh_mac'],
            columnsMapping: [
                'time', // column 0
                'nd_duong_th', // column 1
                'nd_tinh_mac', // column 2
                'note', // column 3
                'xet_nghiem', // column 4
                'y_kien_bs' // column 5
            ],
            defaultOrder: [
                { column: 'id', dir: 'DESC' }
            ],
            checkRole: false
        };

        // Xử lý request
        dataTableService.handleDataTableRequest(req, res, config);
        // try {
        //     var resultMessage = {
        //         "data": [],
        //         "error": "",
        //         "draw": "1",
        //         "recordsFiltered": 0,
        //         "recordsTotal": 0
        //     };
        //     var arrPromise = [],
        //         errors     = [],
        //         parameter  = {
        //             skip: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
        //             take: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
        //             search_value: req.body['search[value]'],
        //             table: 'cat_gan_nho_kpa',
        //             patient_id: req.params.patient_id,
        //             // user: req.user // Thêm thông tin user để áp dụng phân quyền
        //         };
        //     if(!req.user.role_id.includes(5) && !req.user.isAdmin){
        //         resultMessage.error = 'Bạn không có quyền truy cập danh sách này!';
        //         return res.json(resultMessage);
        //     }
        //     arrPromise.push(commonService.countAllBoarding(parameter).then(responseData =>{
        //         if(responseData.success){
        //             if(responseData.data && responseData.data.length > 0){
        //                 let count = responseData.data[0].count;
        //                 resultMessage.recordsFiltered = count;
        //                 resultMessage.recordsTotal = count;
        //             }
        //         }else{
        //             errors.push(responseData.message);
        //         }
        //     }));

        //     arrPromise.push(commonService.getAllBoarding(parameter).then(responseData =>{
        //         if(responseData.success){
        //             if(responseData.data && responseData.data.length > 0) resultMessage.data = responseData.data;
        //         }else{
        //             errors.push(responseData.message);
        //         }
        //     }));
        //     Promise.all(arrPromise).then(()=>{
        //         resultMessage.draw = req.body.draw;
        //         if(errors.length > 0){
        //             resultMessage.error = errors.join(', ');
        //         }
        //         return res.json(resultMessage);
        //     })
        // } catch (error) {
        //     commonService.saveLog(req, error.message, error.stack);
        //     res.json({
        //         "data": [],
        //         "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
        //         "draw": "1",
        //         "recordsFiltered": 0,
        //         "recordsTotal": 0
        //     });
        // }
    },

    // Hàm lấy dữ liệu để export Excel
    getPatientExportData: async function(patientId, path, user) {
        try {
            let exportData = {};

            // Lấy dữ liệu chi tiết dựa trên path
            if (path === 'hoi-chan') {
                // Lấy dữ liệu từ bảng cat_gan_nho_kpa (khẩu phần ăn)
                const kpaResponse = await commonService.getAllDataTable('cat_gan_nho_kpa', {
                    patient_id: patientId,
                    active: 1
                });

                if (kpaResponse.success && kpaResponse.data && kpaResponse.data.length > 0) {
                    // Lấy dữ liệu mới nhất
                    const latestKpa = kpaResponse.data.sort((a, b) => new Date(b.time) - new Date(a.time))[0];
                    
                    // Map dữ liệu theo yêu cầu từ export-excel.md
                    exportData.chandoan = latestKpa.chan_doan || '';
                    exportData.nguyennhan = latestKpa.nguyen_nhan || '';
                    exportData.cannang = latestKpa.cn || '';
                    exportData.chieucao = latestKpa.cc || '';
                    exportData.bapchan = latestKpa.vong_bap_chan || '';
                    exportData.GOT = latestKpa.got || '';
                    exportData.GPT = latestKpa.gpt || '';
                    exportData.Hemo = latestKpa.hemoglobin || '';
                    
                    exportData.buaphu = latestKpa.bua_phu || '';
                    
                    // Xử lý B10_buaphugi - multiple choice
                    if (latestKpa.bua_phu_an) {
                        const buaPhuAnArray = latestKpa.bua_phu_an.toString().split(',').map(x => parseInt(x.trim()));
                        exportData.buaphugi_1 = buaPhuAnArray.includes(1) ? 1 : 0;
                        exportData.buaphugi_2 = buaPhuAnArray.includes(2) ? 1 : 0;
                        exportData.buaphugi_3 = buaPhuAnArray.includes(3) ? 1 : 0;
                        exportData.buaphugi_4 = buaPhuAnArray.includes(4) ? 1 : 0;
                        exportData.buaphugi_5 = buaPhuAnArray.includes(5) ? 1 : 0;
                    }
                    
                    exportData.ankieng = latestKpa.an_kieng || '';
                    
                    // Xử lý B12_kiengi - multiple choice
                    if (latestKpa.an_kieng_loai) {
                        const anKiengArray = latestKpa.an_kieng_loai.toString().split(',').map(x => parseInt(x.trim()));
                        exportData.kiengi_1 = anKiengArray.includes(1) ? 1 : 0;
                        exportData.kiengi_2 = anKiengArray.includes(2) ? 1 : 0;
                        exportData.kiengi_3 = anKiengArray.includes(3) ? 1 : 0;
                        exportData.kiengi_4 = anKiengArray.includes(4) ? 1 : 0;
                        exportData.kiengi_5 = anKiengArray.includes(5) ? 1 : 0;
                    }
                    
                    exportData.ruoubia = latestKpa.ruou_bia || '';
                    exportData.tansuat = latestKpa.ruou_bia_ts || '';
                    exportData.Luongbia = latestKpa.ml_ruou || '';
                    exportData.nuocngot = latestKpa.do_uong_khac || '';
                    exportData.nuocuongkhac = latestKpa.do_uong_khac_ts || '';
                    exportData.douongkhac = latestKpa.loai_do_uong || '';
                    exportData.lacay = latestKpa.su_dung_la_cay || '';
                    exportData.lacaygi = latestKpa.loai_la_cay || '';
                    exportData.ghichu = latestKpa.note || '';
                }

                // Lấy dữ liệu từ bảng cat_gan_nho_sga (nếu có)
                const sgaResponse = await commonService.getAllDataTable('cat_gan_nho_sga', {
                    patient_id: patientId,
                    active: 1
                });

                if (sgaResponse.success && sgaResponse.data && sgaResponse.data.length > 0) {
                    // Xử lý multiple dates cho SGA
                    const sgaList = sgaResponse.data;
                    
                    // Lấy dữ liệu SGA mới nhất
                    const latestSga = sgaList.sort((a, b) => new Date(b.time) - new Date(a.time))[0];
                    
                    exportData.cannang6thang = latestSga.cn_6_thang || '';
                    exportData.tieuhoa = latestSga.tieu_chung_th || '';
                    exportData.Phu = latestSga.phu || '';
                    exportData.Cochuong = latestSga.co_chuong || '';
                    exportData.SGA = latestSga.phan_loai || '';
                }

                // Lấy dữ liệu từ bảng cat_gan_nho_so_gan (nếu có)
                const soGanResponse = await commonService.getAllDataTable('cat_gan_nho_so_gan', {
                    patient_id: patientId,
                    active: 1
                });

                if (soGanResponse.success && soGanResponse.data && soGanResponse.data.length > 0) {
                    const soGan = soGanResponse.data[0];
                    exportData.tinhtranggan = soGan.tinh_trang_gan || '';
                    exportData.mucdoxo = soGan.muc_do_xo_gan || '';
                    exportData.Albumin = soGan.albumin || '';
                    exportData.tuvan = soGan.tu_van_dd || '';
                    exportData.sobuaan = soGan.so_bua_moi_ngay || '';
                    exportData.buadem = soGan.bua_dem || '';
                    exportData.benhkem = soGan.benh_ly_kem_theo || '';
                }

                // Lấy dữ liệu từ bảng cat_gan_nho_kpa (khẩu phần ăn)
                const catGanKpaResponse = await commonService.getAllDataTable('cat_gan_nho_kpa', {
                    patient_id: patientId,
                    active: 1
                });

                if (catGanKpaResponse.success && catGanKpaResponse.data && catGanKpaResponse.data.length > 0) {
                    const catGanKpaList = catGanKpaResponse.data;
                    
                    // Xử lý multiple dates cho cat_gan_nho_kpa
                    if (catGanKpaList.length > 1) {
                        catGanKpaList.forEach((kpa, index) => {
                            const suffix = index + 1;
                            exportData[`cat_gan_kpa_ngay_${suffix}`] = kpa.time ? moment(kpa.time).format('DD/MM/YYYY') : '';
                            exportData[`cat_gan_kpa_nd_duong_th_${suffix}`] = kpa.nd_duong_th;
                            exportData[`cat_gan_kpa_nd_tinh_mac_${suffix}`] = kpa.nd_tinh_mac;
                            exportData[`cat_gan_kpa_note_${suffix}`] = kpa.note;
                            exportData[`cat_gan_kpa_xet_nghiem_${suffix}`] = kpa.xet_nghiem;
                            exportData[`cat_gan_kpa_y_kien_bs_${suffix}`] = kpa.y_kien_bs;
                        });
                    }
                }

                // Lấy dữ liệu từ bảng phieu_hoi_chan_danh_gia
                const phieuDanhGiaResponse = await commonService.getAllDataTable('phieu_hoi_chan_danh_gia', {
                    patient_id: patientId,
                    active: 1
                });

                if (phieuDanhGiaResponse.success && phieuDanhGiaResponse.data && phieuDanhGiaResponse.data.length > 0) {
                    const phieuDanhGiaList = phieuDanhGiaResponse.data;
                    
                    // Xử lý multiple dates cho phieu_hoi_chan_danh_gia
                    if (phieuDanhGiaList.length > 1) {
                        phieuDanhGiaList.forEach((phieu, index) => {
                            const suffix = index + 1;
                            exportData[`phieu_danh_gia_ngay_${suffix}`] = phieu.ngay ? moment(phieu.ngay).format('DD/MM/YYYY') : '';
                            exportData[`phieu_danh_gia_danh_gia_${suffix}`] = phieu.danh_gia;
                            exportData[`phieu_danh_gia_ghichu_${suffix}`] = phieu.ghichu;
                        });
                    }
                }

                // Lấy dữ liệu từ bảng phieu_hoi_chan_ttc
                const phieuTtcResponse = await commonService.getAllDataTable('phieu_hoi_chan_ttc', {
                    patient_id: patientId,
                    active: 1
                });

                if (phieuTtcResponse.success && phieuTtcResponse.data && phieuTtcResponse.data.length > 0) {
                    const phieuTtcList = phieuTtcResponse.data;
                    
                    // Xử lý multiple dates cho phieu_hoi_chan_ttc
                    if (phieuTtcList.length > 1) {
                        phieuTtcList.forEach((phieu, index) => {
                            const suffix = index + 1;
                            exportData[`phieu_ttc_ngay_${suffix}`] = phieu.ngay ? moment(phieu.ngay).format('DD/MM/YYYY') : '';
                            exportData[`phieu_ttc_tinh_trang_${suffix}`] = phieu.tinh_trang;
                            exportData[`phieu_ttc_ghichu_${suffix}`] = phieu.ghichu;
                        });
                    }
                }
            }

            return exportData;

        } catch (error) {
            console.error('Error in getPatientExportData:', error);
            return {};
        }
    }
}

module.exports = liverSurgery;